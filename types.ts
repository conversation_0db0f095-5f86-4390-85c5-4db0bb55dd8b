export interface Step {
  id: number;
  title: string;
  description: string;
  techniques: string[];
  task: 'analyse' | 'génération' | 'validation' | 'synthèse';
}

export interface Message {
  sender: 'user' | 'ai';
  text: string;
}

// Types pour le mode Premium OpenRouter
export interface PremiumUser {
  isAuthenticated: boolean;
  apiKey?: string;
  credits?: number;
  plan?: 'free' | 'premium';
  models?: string[];
}

export interface PremiumModel {
  id: string;
  name: string;
  pricing: {
    prompt: number;
    completion: number;
  };
  context_length: number;
  owned_by: string;
}

export interface OpenRouterResponse {
  models: PremiumModel[];
}

export interface AuthenticationState {
  user: PremiumUser;
  isLoading: boolean;
  error?: string;
}