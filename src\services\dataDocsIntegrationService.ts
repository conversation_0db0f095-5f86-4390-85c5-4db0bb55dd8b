/**
 * Service d'intégration des fichiers Data-Docs
 * Lit et parse les fichiers de techniques avancées de prompting
 * pour enrichir la génération de prompts sans utiliser le vocabulaire explicite
 */

export interface PromptingTechnique {
  id: string;
  name: string;
  description: string;
  implementation: string;
  category: 'structure' | 'reasoning' | 'context' | 'optimization' | 'format';
  complexity: 'basic' | 'intermediate' | 'advanced' | 'expert';
  keywords: string[];
  avoidTerms: string[]; // Termes à éviter dans la génération
}

export interface TemplateStructure {
  id: string;
  name: string;
  sections: TemplateSection[];
  applicableFor: string[];
  complexity: 'basic' | 'intermediate' | 'advanced' | 'expert';
}

export interface TemplateSection {
  title: string;
  description: string;
  placeholder: string;
  required: boolean;
  order: number;
}

export interface AdvancedPromptingKnowledge {
  techniques: PromptingTechnique[];
  templates: TemplateStructure[];
  metaAnalysisGuidelines: string[];
  forbiddenTerms: string[];
  recommendedLLMs: string[];
}

class DataDocsIntegrationService {
  private knowledgeBase: AdvancedPromptingKnowledge | null = null;
  private isInitialized = false;

  /**
   * Initialise le service en chargeant et parsant tous les fichiers Data-Docs
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Charger les fichiers Data-Docs
      const dataPromptsContent = await this.loadDataDocsFile('DATA-Prompts.md');
      const templateMasterContent = await this.loadDataDocsFile('TEMPLATE-MASTER-PRO-ENGINEERING.md');
      const modelesInvitesContent = await this.loadDataDocsFile('Modèles-Invites-Prompting.md');

      // Parser et extraire les connaissances
      this.knowledgeBase = {
        techniques: [
          ...this.extractTechniquesFromDataPrompts(dataPromptsContent),
          ...this.extractTechniquesFromModeles(modelesInvitesContent)
        ],
        templates: this.extractTemplatesFromMaster(templateMasterContent),
        metaAnalysisGuidelines: this.extractMetaAnalysisGuidelines(),
        forbiddenTerms: this.getForbiddenTerms(),
        recommendedLLMs: this.getRecommendedLLMs()
      };

      this.isInitialized = true;
      console.log('✅ Service Data-Docs initialisé avec succès');
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du service Data-Docs:', error);
      // Fallback vers une base de connaissances minimale
      this.initializeFallbackKnowledge();
    }
  }

  /**
   * Charge un fichier depuis le répertoire Data-Docs
   */
  private async loadDataDocsFile(filename: string): Promise<string> {
    try {
      // En production, on utiliserait fetch() ou un système de fichiers
      // Pour l'instant, on simule avec du contenu statique enrichi
      return this.getStaticContent(filename);
    } catch (error) {
      console.warn(`⚠️ Impossible de charger ${filename}, utilisation du contenu de fallback`);
      return '';
    }
  }

  /**
   * Contenu statique enrichi basé sur vos fichiers Data-Docs
   */
  private getStaticContent(filename: string): string {
    const contents: Record<string, string> = {
      'DATA-Prompts.md': `
        Techniques avancées de résolution de problèmes complexes:
        - Analyse multi-dimensionnelle avec décomposition structurée
        - Raisonnement séquentiel mixte (RSM) pour approches hybrides
        - Algorithmes de recherche contextuelle adaptative
        - Méthodologie CSP++ pour contraintes complexes
        - Approche ToT pour exploration d'alternatives
        - Validation croisée CoVe pour vérification
        - Framework CRISPE++ pour structuration avancée
        - Auto-adaptation contextuelle dynamique
      `,
      'TEMPLATE-MASTER-PRO-ENGINEERING.md': `
        Structure de template générique:
        - Analyse du type de problème (optimisation, planification, décision)
        - Définition des objectifs mesurables
        - Identification des contraintes critiques
        - Sélection des sections pertinentes
        - Adaptation automatique du niveau de complexité
        - Génération de livrables structurés
      `,
      'Modèles-Invites-Prompting.md': `
        Catégories de contrôle contextuel:
        - Rôles et expertise spécialisée
        - Temporalité et projections
        - Culture et géographie
        - Domaines de spécialisation
        - Formats de sortie optimisés
        - Techniques de validation
      `
    };
    
    return contents[filename] || '';
  }

  /**
   * Extrait les techniques du fichier DATA-Prompts.md
   */
  private extractTechniquesFromDataPrompts(content: string): PromptingTechnique[] {
    return [
      {
        id: 'rsm_hybrid',
        name: 'Raisonnement Séquentiel Mixte',
        description: 'Combine plusieurs approches méthodologiques pour créer des solutions hybrides',
        implementation: 'Structurer la réponse en étapes logiques, chaque étape utilisant une approche différente selon le contexte',
        category: 'reasoning',
        complexity: 'expert',
        keywords: ['séquentiel', 'hybride', 'multi-approche', 'étapes'],
        avoidTerms: ['chain-of-thought', 'CoT', 'step-by-step']
      },
      {
        id: 'csp_plus',
        name: 'Contraintes Structurées Progressives',
        description: 'Gestion avancée des contraintes avec adaptation progressive',
        implementation: 'Identifier les contraintes par ordre de priorité et les intégrer progressivement dans la solution',
        category: 'structure',
        complexity: 'advanced',
        keywords: ['contraintes', 'progressif', 'priorité', 'adaptation'],
        avoidTerms: ['CSP++', 'constraint satisfaction']
      },
      {
        id: 'contextual_adaptation',
        name: 'Adaptation Contextuelle Dynamique',
        description: 'Ajustement automatique selon le contexte et les besoins spécifiques',
        implementation: 'Analyser le contexte fourni et adapter le style, la profondeur et l\'approche en conséquence',
        category: 'context',
        complexity: 'advanced',
        keywords: ['adaptation', 'contextuel', 'dynamique', 'ajustement'],
        avoidTerms: ['auto-adaptation', 'dynamic context']
      }
    ];
  }

  /**
   * Extrait les techniques du fichier Modèles-Invites-Prompting.md
   */
  private extractTechniquesFromModeles(content: string): PromptingTechnique[] {
    return [
      {
        id: 'role_expertise',
        name: 'Incarnation d\'Expertise Spécialisée',
        description: 'Adoption d\'un rôle d\'expert avec connaissances sectorielles spécifiques',
        implementation: 'Définir un profil d\'expert précis avec expérience, secteur et niveau de séniorité',
        category: 'context',
        complexity: 'intermediate',
        keywords: ['expert', 'rôle', 'spécialisé', 'secteur'],
        avoidTerms: ['role-playing', 'persona', 'character']
      },
      {
        id: 'temporal_framing',
        name: 'Cadrage Temporel Stratégique',
        description: 'Positionnement dans un contexte temporel spécifique pour optimiser la pertinence',
        implementation: 'Spécifier l\'époque, les tendances actuelles et les projections futures pertinentes',
        category: 'context',
        complexity: 'intermediate',
        keywords: ['temporel', 'époque', 'tendances', 'projection'],
        avoidTerms: ['time-framing', 'temporal context']
      },
      {
        id: 'output_optimization',
        name: 'Optimisation des Formats de Sortie',
        description: 'Structuration avancée des réponses selon le format le plus adapté',
        implementation: 'Définir le format de sortie optimal (liste, tableau, analyse, recommandations) selon l\'objectif',
        category: 'format',
        complexity: 'basic',
        keywords: ['format', 'sortie', 'structure', 'optimal'],
        avoidTerms: ['output format', 'response structure']
      }
    ];
  }

  /**
   * Extrait les templates du fichier TEMPLATE-MASTER-PRO-ENGINEERING.md
   */
  private extractTemplatesFromMaster(content: string): TemplateStructure[] {
    return [
      {
        id: 'problem_analysis_template',
        name: 'Template d\'Analyse de Problème',
        sections: [
          {
            title: 'Type de Problème',
            description: 'Classification du problème principal',
            placeholder: 'Optimisation, Planification, Décision, Génération, Analyse...',
            required: true,
            order: 1
          },
          {
            title: 'Objectif Principal',
            description: 'But mesurable à atteindre',
            placeholder: 'Résultat concret et quantifiable souhaité',
            required: true,
            order: 2
          },
          {
            title: 'Contraintes Critiques',
            description: 'Limitations importantes à respecter',
            placeholder: 'Budget, temps, ressources, réglementations...',
            required: true,
            order: 3
          },
          {
            title: 'Contexte Spécifique',
            description: 'Environnement et circonstances particulières',
            placeholder: 'Secteur, taille d\'entreprise, situation actuelle...',
            required: false,
            order: 4
          }
        ],
        applicableFor: ['optimisation', 'planification', 'décision', 'analyse'],
        complexity: 'intermediate'
      }
    ];
  }

  /**
   * Directives pour la méta-analyse (sans Markdown)
   */
  private extractMetaAnalysisGuidelines(): string[] {
    return [
      'Expliquer la logique de construction du prompt en langage naturel',
      'Identifier les techniques utilisées sans mentionner leur nom technique',
      'Justifier les choix structurels par rapport au problème spécifique',
      'Proposer des adaptations possibles selon différents contextes',
      'Éviter tout formatage Markdown (pas d\'astérisques, de tirets, de hashtags)',
      'Utiliser uniquement du texte brut avec des paragraphes séparés',
      'Mettre l\'accent sur la valeur ajoutée de chaque élément du prompt'
    ];
  }

  /**
   * Liste des termes interdits à éviter absolument
   */
  private getForbiddenTerms(): string[] {
    return [
      'chain-of-thought', 'CoT', 'step-by-step', 'role-playing', 'persona',
      'CSP++', 'ToT', 'CoVe', 'CRISPE++', 'auto-adaptation',
      'tree of thoughts', 'constraint satisfaction', 'few-shot',
      'zero-shot', 'prompt engineering', 'prompt chaining'
    ];
  }

  /**
   * LLMs recommandés pour les tests
   */
  private getRecommendedLLMs(): string[] {
    return [
      'ChatGPT (GPT-4, GPT-3.5)',
      'Claude (Anthropic)',
      'Gemini (Google)',
      'Perplexity AI',
      'DeepSeek',
      'Qwen',
      'Mistral AI',
      'Llama (Meta)'
    ];
  }

  /**
   * Initialise une base de connaissances minimale en cas d'échec
   */
  private initializeFallbackKnowledge(): void {
    this.knowledgeBase = {
      techniques: this.extractTechniquesFromDataPrompts(''),
      templates: this.extractTemplatesFromMaster(''),
      metaAnalysisGuidelines: this.extractMetaAnalysisGuidelines(),
      forbiddenTerms: this.getForbiddenTerms(),
      recommendedLLMs: this.getRecommendedLLMs()
    };
    this.isInitialized = true;
  }

  /**
   * Obtient toutes les techniques disponibles
   */
  getTechniques(): PromptingTechnique[] {
    if (!this.isInitialized) {
      console.warn('⚠️ Service non initialisé, utilisation des techniques de base');
      return [];
    }
    return this.knowledgeBase?.techniques || [];
  }

  /**
   * Obtient les techniques adaptées à un type de problème
   */
  getTechniquesForProblem(problemType: string, complexity: string = 'intermediate'): PromptingTechnique[] {
    const techniques = this.getTechniques();
    return techniques.filter(tech => 
      tech.complexity === complexity || 
      (complexity === 'advanced' && tech.complexity === 'expert')
    );
  }

  /**
   * Obtient les templates disponibles
   */
  getTemplates(): TemplateStructure[] {
    return this.knowledgeBase?.templates || [];
  }

  /**
   * Obtient un template adapté au type de problème
   */
  getTemplateForProblem(problemType: string): TemplateStructure | null {
    const templates = this.getTemplates();
    return templates.find(template => 
      template.applicableFor.includes(problemType.toLowerCase())
    ) || templates[0] || null;
  }

  /**
   * Obtient les directives de méta-analyse
   */
  getMetaAnalysisGuidelines(): string[] {
    return this.knowledgeBase?.metaAnalysisGuidelines || [];
  }

  /**
   * Vérifie si un terme est interdit
   */
  isForbiddenTerm(term: string): boolean {
    const forbiddenTerms = this.knowledgeBase?.forbiddenTerms || [];
    return forbiddenTerms.some(forbidden => 
      term.toLowerCase().includes(forbidden.toLowerCase())
    );
  }

  /**
   * Obtient la liste des LLMs recommandés
   */
  getRecommendedLLMs(): string[] {
    return this.knowledgeBase?.recommendedLLMs || [];
  }

  /**
   * Obtient la liste des termes interdits
   */
  getForbiddenTermsList(): string[] {
    return this.knowledgeBase?.forbiddenTerms || [];
  }

  /**
   * Génère un prompt système enrichi avec les techniques avancées
   */
  generateEnhancedSystemPrompt(problemDescription: string): string {
    const techniques = this.getTechniquesForProblem('general', 'advanced');
    const guidelines = this.getMetaAnalysisGuidelines();
    
    let prompt = `INSTRUCTION CRITIQUE: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Aucun mot ou expression en anglais n'est autorisé.

Vous êtes ROONY, expert en résolution de problèmes complexes et en optimisation de solutions. Votre mission est de créer un prompt hautement optimisé basé sur la description du problème.

TECHNIQUES AVANCÉES À INTÉGRER SUBTILEMENT:
`;

    // Ajouter les techniques sans mentionner leur nom technique
    techniques.forEach(tech => {
      prompt += `- ${tech.description}: ${tech.implementation}\n`;
    });

    prompt += `
STRUCTURE OBLIGATOIRE DE VOTRE RÉPONSE:

### 🎯 Le Prompt Optimisé

[Créez un prompt complet et autonome intégrant les techniques avancées de manière naturelle]

---

### 🔍 Méta-Analyse de la Construction

[Analysez votre construction en respectant ces directives:]
`;

    guidelines.forEach(guideline => {
      prompt += `- ${guideline}\n`;
    });

    prompt += `
---

### 💡 Recommandations Stratégiques

[Proposez 4-5 recommandations concrètes pour optimiser l'utilisation]

RÈGLES CRITIQUES:
- Le prompt généré doit être AUTONOME et directement utilisable
- Intégrez les techniques avancées de manière invisible et naturelle
- AUCUN formatage Markdown dans la méta-analyse (texte brut uniquement)
- Évitez absolument tout vocabulaire technique de prompting
- Optimisez pour la clarté, l'efficacité et la robustesse

RAPPEL: Votre réponse complète doit être en français uniquement.`;

    return prompt;
  }
}

// Instance singleton
export const dataDocsIntegrationService = new DataDocsIntegrationService();
