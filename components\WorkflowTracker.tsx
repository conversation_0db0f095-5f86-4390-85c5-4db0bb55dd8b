import React, { useState } from 'react';
import type { Step } from '../types';

interface WorkflowTrackerProps {
  steps: Step[];
  currentStepIndex: number;
}

export const WorkflowTracker: React.FC<WorkflowTrackerProps> = ({ steps, currentStepIndex }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const currentStep = steps[currentStepIndex];
  const progressPercentage = currentStepIndex >= steps.length ? 100 : (currentStepIndex / (steps.length - 1)) * 100;

  return (
    <div className="w-full bg-slate-900/50 rounded-2xl border border-slate-700 overflow-hidden">
      {/* En-tête toujours visible */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center justify-between hover:bg-slate-800/50 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-emerald-500/20 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-left">
            <div className="text-sm font-semibold text-emerald-300">Analyse Complète</div>
            <div className="text-xs text-slate-400">
              Étape {Math.min(currentStepIndex + 1, steps.length)} / {steps.length} • {Math.round(progressPercentage)}%
            </div>
          </div>
        </div>

        <svg
          className={`w-4 h-4 text-emerald-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Barre de progression toujours visible */}
      <div className="px-3 pb-2">
        <div className="w-full bg-slate-700 rounded-full h-1.5">
          <div
            className="bg-gradient-to-r from-purple-500 to-indigo-500 h-1.5 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Contenu détaillé (accordéon) */}
      {isExpanded && (
        <div className="px-3 pb-3 border-t border-slate-700/50">
          <div className="bg-indigo-900/10 rounded p-3 mt-3 border border-indigo-500/20">
            {currentStep ? (
              <div>
                <h4 className="font-bold text-slate-100 mb-1">{currentStep.title}</h4>
                <p className="text-xs text-slate-400 mb-2">{currentStep.description}</p>
                <div className="flex flex-wrap gap-1">
                  {currentStep.techniques.map((technique, index) => (
                    <span key={index} className="text-xs bg-indigo-500/20 text-indigo-300 px-2 py-1 rounded">
                      {technique}
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                <h4 className="font-bold text-green-300 mb-1">✅ Workflow Terminé</h4>
                <p className="text-xs text-slate-400">Le prompt final a été généré avec succès.</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
