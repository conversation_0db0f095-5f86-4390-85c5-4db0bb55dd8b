{"name": "roony-studio-agent<PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "gen-deps": "node .Dev/Code_Base-Index/generate_dependency_map.cjs"}, "dependencies": {"@google/genai": "^1.15.0", "gsap": "^3.13.0", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "typescript": "~5.8.2", "vite": "^6.2.0"}}