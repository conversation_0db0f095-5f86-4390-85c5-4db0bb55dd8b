import type { Message } from '../types';
import { operationalFeasibilityFilter, type ActionToValidate } from '../src/services/operationalFeasibilityFilter';

export interface WorkflowInsight {
  stepIndex: number;
  stepTitle: string;
  userInput: string;
  aiAnalysis: string;
  keyRecommendations: string[];
  timestamp: Date;
}

export interface FinalActionPlan {
  executiveSummary: string;
  keyFindings: string[];
  prioritizedActions: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    action: string;
    rationale: string;
    timeline: string;
    feasibilityValidated?: boolean;
    originalAction?: string; // Pour garder une trace de l'action originale
  }[];
  nextSteps: string[];
  successMetrics: string[];
  potentialRisks: string[];
  feasibilityReport?: string; // Rapport du filtre de faisabilité
  overallFeasibilityScore?: 'HIGH' | 'MEDIUM' | 'LOW';
}

class WorkflowMemoryService {
  private insights: WorkflowInsight[] = [];
  private finalPlan: FinalActionPlan | null = null;

  /**
   * Ajouter une nouvelle analyse d'étape
   */
  addInsight(insight: WorkflowInsight): void {
    this.insights.push(insight);
    console.log(`📝 Nouvelle analyse ajoutée pour l'étape ${insight.stepIndex}: ${insight.stepTitle}`);
  }

  /**
   * Extraire automatiquement les recommandations d'un texte IA
   */
  extractRecommendations(aiText: string): string[] {
    const recommendations: string[] = [];
    
    // Rechercher des patterns de recommandations
    const patterns = [
      /(?:je recommande|je suggère|il faudrait|vous devriez|il serait bien de|considérez)\s+([^.!?]+)/gi,
      /(?:recommandation|suggestion|conseil)\s*:\s*([^.!?]+)/gi,
      /(?:étape suivante|prochaine étape|action)\s*:\s*([^.!?]+)/gi,
      /(?:important|crucial|essentiel)\s+(?:de|d')\s+([^.!?]+)/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(aiText)) !== null) {
        const recommendation = match[1].trim();
        if (recommendation.length > 10 && !recommendations.includes(recommendation)) {
          recommendations.push(recommendation);
        }
      }
    });

    return recommendations.slice(0, 5); // Limiter à 5 recommandations max
  }

  /**
   * Analyser une conversation et extraire les insights
   */
  analyzeConversationStep(
    stepIndex: number,
    stepTitle: string,
    userMessage: string,
    aiResponse: string
  ): void {
    const recommendations = this.extractRecommendations(aiResponse);
    
    const insight: WorkflowInsight = {
      stepIndex,
      stepTitle,
      userInput: userMessage,
      aiAnalysis: aiResponse,
      keyRecommendations: recommendations,
      timestamp: new Date()
    };

    this.addInsight(insight);
  }

  /**
   * Générer le plan d'action final basé sur tous les insights
   */
  async generateFinalActionPlan(initialProblem: string): Promise<FinalActionPlan> {
    if (this.insights.length === 0) {
      throw new Error('Aucune analyse disponible pour générer le plan final');
    }

    // Consolider toutes les recommandations
    const allRecommendations = this.insights.flatMap(insight => insight.keyRecommendations);
    const uniqueRecommendations = [...new Set(allRecommendations)];

    // Analyser les patterns et priorités
    const highPriorityKeywords = ['urgent', 'critique', 'immédiat', 'essentiel', 'crucial'];
    const mediumPriorityKeywords = ['important', 'recommandé', 'suggéré', 'devrait'];

    const prioritizedActions = uniqueRecommendations.map(recommendation => {
      let priority: 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW';
      
      if (highPriorityKeywords.some(keyword => 
        recommendation.toLowerCase().includes(keyword))) {
        priority = 'HIGH';
      } else if (mediumPriorityKeywords.some(keyword => 
        recommendation.toLowerCase().includes(keyword))) {
        priority = 'MEDIUM';
      }

      return {
        priority,
        action: recommendation,
        rationale: `Basé sur l'analyse des étapes du workflow`,
        timeline: priority === 'HIGH' ? 'Immédiat' : priority === 'MEDIUM' ? '1-2 semaines' : '1 mois',
        originalAction: recommendation // Garder une trace de l'action originale
      };
    }).sort((a, b) => {
      const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    // APPLICATION DU PRINCIPE 5 : Filtre de Faisabilité Opérationnelle
    const actionsToValidate: ActionToValidate[] = prioritizedActions.map((action, index) => ({
      title: `Action ${index + 1}`,
      description: action.action,
      timeline: action.timeline,
      priority: action.priority,
      order: index + 1,
      context: {
        domain: this.extractDomainFromInsights(),
        userSituation: initialProblem,
        countries: this.extractCountriesFromInsights(),
        sectors: this.extractSectorsFromInsights()
      }
    }));

    // Validation de faisabilité
    const feasibilityResults = operationalFeasibilityFilter.validateActionPlan(actionsToValidate);
    const feasibilityReport = operationalFeasibilityFilter.generateFeasibilityReport(actionsToValidate);

    // Mise à jour des actions avec les améliorations
    const validatedActions = prioritizedActions.map((action, index) => {
      const result = feasibilityResults[index];
      
      return {
        ...action,
        action: result.improvedAction || action.action,
        feasibilityValidated: result.isValid,
        originalAction: result.improvedAction ? action.action : undefined
      };
    });

    // Calcul du score global de faisabilité
    const criticalIssues = feasibilityResults.filter(r => 
      r.issues.some(i => i.severity === 'CRITICAL')
    ).length;
    const totalIssues = feasibilityResults.reduce((sum, result) => sum + result.issues.length, 0);
    
    let overallFeasibilityScore: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';
    if (criticalIssues > 0) {
      overallFeasibilityScore = 'LOW';
    } else if (totalIssues > 3) {
      overallFeasibilityScore = 'MEDIUM';
    }

    // Générer le résumé exécutif
    const executiveSummary = `Analyse complète du problème "${initialProblem}" à travers ${this.insights.length} étapes de workflow. ${uniqueRecommendations.length} recommandations identifiées avec ${prioritizedActions.filter(a => a.priority === 'HIGH').length} actions prioritaires.`;

    // Extraire les principales découvertes
    const keyFindings = this.insights.slice(0, 5).map(insight => 
      `Étape ${insight.stepIndex} (${insight.stepTitle}): ${insight.aiAnalysis.substring(0, 150)}...`
    );

    this.finalPlan = {
      executiveSummary,
      keyFindings,
      prioritizedActions: validatedActions.slice(0, 10), // Top 10 actions
      nextSteps: [
        'Réviser et valider le plan d\'action proposé',
        'Prioriser les actions selon les ressources disponibles',
        'Définir les responsabilités et échéances',
        'Mettre en place un système de suivi des progrès'
      ],
      successMetrics: [
        'Résolution du problème initial',
        'Mise en œuvre des actions prioritaires',
        'Amélioration mesurable des indicateurs clés',
        'Satisfaction des parties prenantes'
      ],
      potentialRisks: [
        'Manque de ressources pour l\'exécution',
        'Résistance au changement',
        'Complexité technique sous-estimée',
        'Délais trop optimistes'
      ],
      feasibilityReport,
      overallFeasibilityScore
    };

    return this.finalPlan;
  }

  /**
   * Extrait le domaine principal des insights collectés
   */
  private extractDomainFromInsights(): string {
    const allText = this.insights.map(i => `${i.userInput} ${i.aiAnalysis}`).join(' ').toLowerCase();
    
    if (allText.includes('immigration') || allText.includes('titre') || allText.includes('visa')) {
      return 'immigration';
    }
    if (allText.includes('urssaf') || allText.includes('cotisation') || allText.includes('social')) {
      return 'social';
    }
    if (allText.includes('entreprise') || allText.includes('business') || allText.includes('commercial')) {
      return 'business';
    }
    
    return 'general';
  }

  /**
   * Extrait les pays mentionnés dans les insights
   */
  private extractCountriesFromInsights(): string[] {
    const countries: string[] = [];
    const allText = this.insights.map(i => `${i.userInput} ${i.aiAnalysis}`).join(' ').toLowerCase();
    
    const countryKeywords = [
      'france', 'allemagne', 'espagne', 'italie', 'belgique', 
      'suisse', 'canada', 'maroc', 'algérie', 'tunisie'
    ];
    
    countryKeywords.forEach(country => {
      if (allText.includes(country)) {
        countries.push(country);
      }
    });
    
    return countries;
  }

  /**
   * Extrait les secteurs mentionnés dans les insights
   */
  private extractSectorsFromInsights(): string[] {
    const sectors: string[] = [];
    const allText = this.insights.map(i => `${i.userInput} ${i.aiAnalysis}`).join(' ').toLowerCase();
    
    const sectorKeywords = [
      'santé', 'éducation', 'finance', 'technologie', 'immobilier',
      'commerce', 'industrie', 'agriculture', 'tourisme', 'transport'
    ];
    
    sectorKeywords.forEach(sector => {
      if (allText.includes(sector)) {
        sectors.push(sector);
      }
    });
    
    return sectors;
  }

  /**
   * Obtenir tous les insights
   */
  getAllInsights(): WorkflowInsight[] {
    return [...this.insights];
  }

  /**
   * Obtenir le plan final
   */
  getFinalPlan(): FinalActionPlan | null {
    return this.finalPlan;
  }

  /**
   * Réinitialiser la mémoire
   */
  reset(): void {
    this.insights = [];
    this.finalPlan = null;
    console.log('🔄 Mémoire du workflow réinitialisée');
  }

  /**
   * Exporter les données pour sauvegarde
   */
  exportData(): { insights: WorkflowInsight[], finalPlan: FinalActionPlan | null } {
    return {
      insights: this.insights,
      finalPlan: this.finalPlan
    };
  }

  /**
   * Importer des données sauvegardées
   */
  importData(data: { insights: WorkflowInsight[], finalPlan: FinalActionPlan | null }): void {
    this.insights = data.insights || [];
    this.finalPlan = data.finalPlan;
    console.log(`📥 Données importées: ${this.insights.length} insights`);
  }
}

// Instance singleton
export const workflowMemoryService = new WorkflowMemoryService();
