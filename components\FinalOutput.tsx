import React, { useState, useEffect } from 'react';
import { generateStrategicReport, exportToPDF, type ReportData } from '../services/reportService';
import { workflowMemoryService, type FinalActionPlan } from '../services/workflowMemoryService';
import type { Message } from '../types';
import RoonyInlineAnimation from './RoonyInlineAnimation';

interface FinalOutputProps {
  finalPrompt: string;
  onStartNewWorkflow?: () => void;
  onRefinePrompt?: () => void;
  // Nouvelles props pour le rapport stratégique
  initialProblem?: string;
  conversation?: Message[];
  reasoningLog?: string[];
  currentStepIndex?: number;
}

const CopyIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
);

export const FinalOutput: React.FC<FinalOutputProps> = ({
  finalPrompt,
  onStartNewWorkflow,
  onRefinePrompt,
  initialProblem = '',
  conversation = [],
  reasoningLog = [],
  currentStepIndex = 0
}) => {
    const [copyButtonText, setCopyButtonText] = useState('Copier');
    const [finalPlan, setFinalPlan] = useState<FinalActionPlan | null>(null);
    const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
    const [showCelebration, setShowCelebration] = useState(false);

    // Déclencher l'animation de célébration quand le composant se monte
    useEffect(() => {
        const timer = setTimeout(() => {
            setShowCelebration(true);
        }, 500);
        return () => clearTimeout(timer);
    }, []);
    
    const parts = finalPrompt.split('---');
    const optimizedPrompt = parts[0]?.replace('### Le Prompt Optimisé', '').trim() || '';
    const metaAnalysis = parts[1]?.replace('### Méta-Analyse de la Construction', '').trim() || '';

    const handleCopy = () => {
        navigator.clipboard.writeText(optimizedPrompt).then(() => {
            setCopyButtonText('Copié !');
            setTimeout(() => setCopyButtonText('Copier'), 2000);
        });
    };
    
    const handleExport = (format: 'txt' | 'json' | 'md') => {
        let content = '';
        let mimeType = '';
        let filename = `prompt-optimise.${format}`;

        const fullMarkdown = `### Le Prompt Optimisé\n\n${optimizedPrompt}\n\n---\n\n### Méta-Analyse de la Construction\n\n${metaAnalysis}`;

        switch (format) {
            case 'txt':
                content = `Le Prompt Optimisé:\n${optimizedPrompt}\n\n---\n\nMéta-Analyse de la Construction:\n${metaAnalysis}`;
                mimeType = 'text/plain';
                break;
            case 'json':
                content = JSON.stringify({
                    prompt: optimizedPrompt,
                    metaAnalyse: metaAnalysis
                }, null, 2);
                mimeType = 'application/json';
                break;
            case 'md':
                content = fullMarkdown;
                mimeType = 'text/markdown';
                break;
        }

        const blob = new Blob([content], { type: mimeType, endings: 'native' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const handleExportReport = (format: 'pdf' | 'md') => {
        const reportData: ReportData = {
            initialProblem,
            conversation,
            finalPrompt,
            reasoningLog,
            currentStepIndex
        };

        const reportContent = generateStrategicReport(reportData);

        if (format === 'pdf') {
            exportToPDF(reportContent, `rapport-strategique-roony-${new Date().toISOString().split('T')[0]}`);
        } else {
            // Export en Markdown
            const blob = new Blob([reportContent], { type: 'text/markdown', endings: 'native' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rapport-strategique-roony-${new Date().toISOString().split('T')[0]}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    };

    const handleGenerateFinalPlan = async () => {
        setIsGeneratingPlan(true);
        try {
            const plan = await workflowMemoryService.generateFinalActionPlan(initialProblem);
            setFinalPlan(plan);
        } catch (error) {
            console.error('Erreur lors de la génération du plan final:', error);
        } finally {
            setIsGeneratingPlan(false);
        }
    };

  return (
    <div className="flex-grow flex flex-col p-6 overflow-y-auto">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-teal-500">
          Résultat Final du Workflow
        </h2>

        {/* Animation de célébration de Roony */}
        <RoonyInlineAnimation
          trigger={showCelebration}
          animation="proud"
          size={100}
          duration={3500}
          className="flex-shrink-0"
          onComplete={() => setShowCelebration(false)}
        />
      </div>
      
      <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700 mb-6">
        <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-green-300">Prompt Optimisé</h3>
            <button 
                onClick={handleCopy}
                className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-1 px-3 rounded-md flex items-center transition-colors"
            >
               <CopyIcon /> {copyButtonText}
            </button>
        </div>
        <div className="text-slate-300 whitespace-pre-wrap font-mono text-sm p-3 bg-black/20 rounded-md">
            {optimizedPrompt}
        </div>
      </div>
      
      <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700">
        <h3 className="text-lg font-semibold text-teal-300 mb-2">Méta-Analyse de la Construction</h3>
        <div className="text-slate-400 prose prose-sm prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: metaAnalysis.replace(/\n/g, '<br />') }} />
      </div>
      
      {/* Section Export - Divisée en deux parties */}
      <div className="mt-6 pt-4 border-t border-slate-700">
        {/* Export du Prompt */}
        <div className="text-center mb-6">
          <h4 className="text-md font-semibold mb-3 text-sky-300">📄 Exporter le Prompt</h4>
          <div className="flex gap-2 justify-center">
            <button onClick={() => handleExport('md')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors">.md</button>
            <button onClick={() => handleExport('json')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors">.json</button>
            <button onClick={() => handleExport('txt')} className="bg-sky-600 hover:bg-sky-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors">.txt</button>
          </div>
        </div>

        {/* Export du Rapport Stratégique */}
        <div className="text-center">
          <h4 className="text-md font-semibold mb-3 text-emerald-300">📊 Rapport Stratégique Complet</h4>
          <p className="text-sm text-slate-400 mb-3">
            Téléchargez un rapport détaillé avec toutes les recommandations, l'analyse du workflow et les prochaines étapes
          </p>
          <div className="flex gap-2 justify-center">
            <button
              onClick={() => handleExportReport('pdf')}
              className="bg-emerald-600 hover:bg-emerald-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              PDF
            </button>
            <button
              onClick={() => handleExportReport('md')}
              className="bg-emerald-600 hover:bg-emerald-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Markdown
            </button>
          </div>
        </div>

        {/* Bouton Plan d'Action Final */}
        <div className="text-center mt-6">
          <h4 className="text-md font-semibold mb-3 text-purple-300">🎯 Plan d'Action Stratégique</h4>
          <p className="text-sm text-slate-400 mb-3">
            Générez un plan d'action détaillé basé sur toute l'analyse du workflow
          </p>
          <button
            onClick={handleGenerateFinalPlan}
            disabled={isGeneratingPlan}
            className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 disabled:from-slate-600 disabled:to-slate-600 text-white text-sm font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2 mx-auto"
          >
            {isGeneratingPlan ? (
              <>
                <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Génération du plan...
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Générer Plan d'Action Final
              </>
            )}
          </button>
        </div>
      </div>

      {/* Affichage du Plan Final si généré */}
      {finalPlan && (
        <div className="mt-6 p-6 bg-gradient-to-r from-emerald-900/30 to-teal-900/30 rounded-xl border border-emerald-500/30">
          <h4 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-teal-400 mb-4 text-center">
            📋 Plan d'Action Final Généré
          </h4>

          <div className="space-y-4">
            <div className="bg-slate-800/50 p-4 rounded-lg">
              <h5 className="text-sm font-semibold text-emerald-300 mb-2">Résumé Exécutif</h5>
              <p className="text-sm text-slate-300">{finalPlan.executiveSummary}</p>
            </div>

            <div className="bg-slate-800/50 p-4 rounded-lg">
              <h5 className="text-sm font-semibold text-red-300 mb-2">
                Actions Haute Priorité ({finalPlan.prioritizedActions.filter(a => a.priority === 'HIGH').length})
              </h5>
              <ul className="space-y-2">
                {finalPlan.prioritizedActions.filter(a => a.priority === 'HIGH').slice(0, 5).map((action, index) => (
                  <li key={index} className="text-sm text-slate-300 flex items-start gap-2">
                    <span className="text-red-400 mt-1">🔴</span>
                    <div>
                      <div className="font-medium">{action.action}</div>
                      <div className="text-xs text-slate-400">Délai: {action.timeline}</div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            <div className="flex gap-2 justify-center">
              <button
                onClick={() => {
                  const content = `# Plan d'Action Final\n\n## Résumé\n${finalPlan.executiveSummary}\n\n## Actions Prioritaires\n${finalPlan.prioritizedActions.map(a => `- ${a.action} (${a.priority})`).join('\n')}`;
                  const blob = new Blob([content], { type: 'text/markdown' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `plan-action-final-${new Date().toISOString().split('T')[0]}.md`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
                className="bg-emerald-600 hover:bg-emerald-500 text-white text-sm font-semibold py-2 px-4 rounded-md transition-colors flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Télécharger Plan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Section d'engagement utilisateur */}
      <div className="mt-8 p-6 bg-gradient-to-r from-indigo-900/30 to-purple-900/30 rounded-xl border border-indigo-500/30">
        <h4 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400 mb-4 text-center">
          🎯 Et maintenant, que souhaitez-vous faire ?
        </h4>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {onStartNewWorkflow && (
              <button
                onClick={onStartNewWorkflow}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Nouveau Workflow
              </button>
            )}

            {onRefinePrompt && (
              <button
                onClick={onRefinePrompt}
                className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-500 hover:to-red-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Affiner ce Prompt
              </button>
            )}
          </div>

          <div className="text-center">
            <p className="text-slate-400 text-sm mb-3">
              💡 <strong>Suggestions d'actions :</strong>
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
              <div className="bg-slate-800/50 p-3 rounded-lg border border-slate-600">
                <div className="text-blue-400 font-semibold mb-1">🔄 Itérer</div>
                <div className="text-slate-300">Testez votre prompt et revenez pour l'améliorer</div>
              </div>
              <div className="bg-slate-800/50 p-3 rounded-lg border border-slate-600">
                <div className="text-green-400 font-semibold mb-1">🚀 Déployer</div>
                <div className="text-slate-300">Utilisez votre prompt dans vos projets IA</div>
              </div>
              <div className="bg-slate-800/50 p-3 rounded-lg border border-slate-600">
                <div className="text-purple-400 font-semibold mb-1">📚 Partager</div>
                <div className="text-slate-300">Documentez et partagez votre solution</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};