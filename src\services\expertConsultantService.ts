/**
 * Service Expert-Conseil Stratégique pour Rooney
 * Transforme Rooney en un véritable STRATÈGE avec vision d'expert
 * 
 * Implémente les 5 principes fondamentaux + LES AMÉLIORATIONS STRATÉGIQUES :
 * 1. L'Hypothèse Prime sur la Question
 * 2. Dissocier et Hiérarchiser les Pistes d'Analyse  
 * 3. Mobiliser la Connaissance Fondamentale
 * 4. Structurer comme un Plan d'Action
 * 5. Comportement Humain Naturel
 * 
 * + NOUVEAUX POUVOIRS STRATÉGIQUES :
 * 6. Fil Rouge Stratégique : Identifier et maintenir l'objectif prioritaire central
 * 7. Pépites de Valeur : Enrichir chaque étape avec conseils proactifs et outils concrets
 * 8. Vérification Terminologique Expert : Précision et expertise dans le vocabulaire spécialisé
 */

import type { Step } from '../../types';
import { knowledgeBaseService } from './knowledgeBaseService';
import { hypothesisGeneratorService, type HypothesisContext } from './hypothesisGeneratorService';
import { problemDecompositionService } from './problemDecompositionService';
import { actionPlanFormatterService } from './actionPlanFormatterService';
import { contextFileService, type ContextFile } from './contextFileService';

export interface ExpertAnalysis {
  fundamentalPrinciples: string[];
  hypotheses: string[];
  problemDecomposition: ProblemEntity[];
  actionPlan: ActionPlanSection[];
  humanTouch: HumanResponse;
  strategicThread: StrategicThread;        // NOUVEAU : Fil rouge stratégique
  valueGems: ValueGem[];                   // NOUVEAU : Pépites de valeur
  expertTerminology: ExpertTerminology;    // NOUVEAU : Vérification terminologique
}

export interface StrategicThread {
  coreObjective: string;               // L'objectif prioritaire central
  leveragePoint: string;               // Le point de levier principal
  continuityCheck: string[];           // Vérifications de cohérence avec l'objectif
  impactMeasurement: string[];         // Comment mesurer l'avancement vers l'objectif
}

export interface ValueGem {
  category: 'tool' | 'insight' | 'contact' | 'template' | 'technique' | 'warning';
  title: string;
  description: string;
  actionableAdvice: string;
  insiderTip?: string;                 // Astuce d'initié
  templateOrModel?: string;            // Modèle de document concret
  relevantContacts?: string[];         // Contacts ou organismes pertinents
}

export interface ExpertTerminology {
  domainTerms: TerminologyCheck[];     // Vérification des termes du domaine
  precisionAlerts: string[];           // Alertes de précision terminologique
  expertCorrections: TermCorrection[]; // Corrections expertes nécessaires
}

export interface TerminologyCheck {
  term: string;
  isCorrect: boolean;
  domain: string;
  correctTerm?: string;
  explanation?: string;
}

export interface TermCorrection {
  incorrectUsage: string;
  correctUsage: string;
  context: string;
  importance: 'critical' | 'important' | 'minor';
}

export interface ProblemEntity {
  name: string;
  type: 'administrative' | 'legal' | 'financial' | 'social';
  description: string;
  independence: boolean;
  interactions: string[];
}

export interface ActionPlanSection {
  title: string;
  content: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  timeline: string;
}

export interface HumanResponse {
  empathyPhrase: string;
  conversationalTransitions: string[];
  reassuringElements: string[];
  warmClosing: string;
}

class ExpertConsultantService {
  
  /**
   * NOUVEAU : Identifie le fil rouge stratégique du problème
   * L'objectif prioritaire et le point de levier principal
   */
  private identifyStrategicThread(problem: string, keywords: string[], domain: string): StrategicThread {
    // Analyser le problème pour identifier l'objectif central
    const coreObjective = this.extractCoreObjective(problem);
    const leveragePoint = this.identifyLeveragePoint(problem, keywords, domain);
    
    return {
      coreObjective,
      leveragePoint,
      continuityCheck: this.generateContinuityChecks(coreObjective),
      impactMeasurement: this.generateImpactMeasurements(coreObjective, domain)
    };
  }

  /**
   * Extrait l'objectif prioritaire central du problème
   */
  private extractCoreObjective(problem: string): string {
    const objectiveIndicators = [
      { pattern: /besoin (de|d')(.+)/gi, template: "Satisfaire le besoin de $2" },
      { pattern: /veux (.+)/gi, template: "Réaliser : $1" },
      { pattern: /cherche (à|à )(.+)/gi, template: "Accomplir : $2" },
      { pattern: /problème (.+)/gi, template: "Résoudre : $1" },
      { pattern: /situation (.+)/gi, template: "Améliorer la situation : $1" },
      { pattern: /obtenir (.+)/gi, template: "Obtenir : $1" },
      { pattern: /réussir (.+)/gi, template: "Réussir : $1" }
    ];

    for (const indicator of objectiveIndicators) {
      const match = problem.match(indicator.pattern);
      if (match) {
        return indicator.template.replace(/\$(\d+)/g, (_, num) => match[parseInt(num)] || '');
      }
    }

    // Objectif par défaut si rien de spécifique n'est détecté
    return "Résoudre la situation décrite de manière optimale et durable";
  }

  /**
   * Identifie le point de levier principal (où concentrer l'effort)
   */
  private identifyLeveragePoint(problem: string, keywords: string[], domain: string): string {
    const leveragePoints = {
      'administrative': [
        { triggers: ['préfecture', 'titre', 'séjour'], point: "Maîtriser la procédure administrative centrale" },
        { triggers: ['urssaf', 'cotisations'], point: "Optimiser les déclarations et paiements sociaux" },
        { triggers: ['visa', 'autorisation'], point: "Sécuriser le statut légal" }
      ],
      'legal': [
        { triggers: ['contrat', 'accord'], point: "Sécuriser l'encadrement juridique" },
        { triggers: ['litige', 'conflit'], point: "Résoudre le différend rapidement" },
        { triggers: ['droits', 'protection'], point: "Faire valoir les droits fondamentaux" }
      ],
      'financial': [
        { triggers: ['budget', 'financement'], point: "Optimiser la structure financière" },
        { triggers: ['aide', 'subvention'], point: "Maximiser les ressources disponibles" },
        { triggers: ['coût', 'économie'], point: "Contrôler les dépenses essentielles" }
      ]
    };

    const domainPoints = leveragePoints[domain as keyof typeof leveragePoints] || [];
    
    for (const leveragePoint of domainPoints) {
      if (leveragePoint.triggers.some(trigger => 
        keywords.includes(trigger) || problem.toLowerCase().includes(trigger)
      )) {
        return leveragePoint.point;
      }
    }

    return "Identifier et agir sur l'élément critique qui débloquera la situation";
  }

  /**
   * Génère les vérifications de continuité avec l'objectif central
   */
  private generateContinuityChecks(coreObjective: string): string[] {
    return [
      `Cette action contribue-t-elle directement à : ${coreObjective} ?`,
      "Cette recommandation garde-t-elle le cap sur la priorité principale ?",
      "Cette étape rapproche-t-elle concrètement du résultat souhaité ?",
      "Cette approche optimise-t-elle l'effort vers l'objectif central ?"
    ];
  }

  /**
   * Génère les métriques d'impact pour mesurer l'avancement
   */
  private generateImpactMeasurements(coreObjective: string, domain: string): string[] {
    const baseMeasurements = [
      "Progression mesurable vers l'objectif principal",
      "Réduction des obstacles identifiés",
      "Amélioration de la situation globale"
    ];

    const domainSpecificMeasurements = {
      'administrative': [
        "Avancement dans les procédures officielles",
        "Réduction des délais administratifs",
        "Sécurisation du statut légal"
      ],
      'legal': [
        "Clarification des droits et obligations",
        "Résolution des points de conflit",
        "Sécurisation juridique renforcée"
      ],
      'financial': [
        "Optimisation des ressources financières",
        "Réduction des coûts inutiles",
        "Amélioration de la viabilité économique"
      ]
    };

    return [
      ...baseMeasurements,
      ...(domainSpecificMeasurements[domain as keyof typeof domainSpecificMeasurements] || [])
    ];
  }

  /**
   * NOUVEAU : Génère les pépites de valeur pour enrichir l'analyse
   */
  private generateValueGems(problem: string, keywords: string[], domain: string, stepTitle: string): ValueGem[] {
    const gems: ValueGem[] = [];

    // Pépites basées sur le domaine
    gems.push(...this.getDomainSpecificGems(domain, keywords));
    
    // Pépites basées sur le type d'étape
    gems.push(...this.getStepSpecificGems(stepTitle, problem));
    
    // Pépites proactives génériques
    gems.push(...this.getProactiveGems(problem, keywords));

    return gems.slice(0, 5); // Limiter à 5 pépites par analyse
  }

  /**
   * Pépites spécifiques au domaine
   */
  private getDomainSpecificGems(domain: string, keywords: string[]): ValueGem[] {
    const domainGems = {
      'administrative': [
        {
          category: 'template' as const,
          title: "Modèle de Lettre Administrative",
          description: "Template optimisé pour les démarches officielles",
          actionableAdvice: "Utilisez toujours un format officiel avec références précises",
          insiderTip: "Mentionnez toujours votre numéro de dossier en objet",
          templateOrModel: `
MODÈLE DE LETTRE ADMINISTRATIVE:
[Vos coordonnées]
[Date]
[Service destinataire]
Objet: [Précis et avec numéro de dossier]
Madame, Monsieur,
[Contexte en 1 phrase]
[Demande précise]
[Justification avec références légales]
[Formule de politesse]
[Signature]
Pièces jointes: [Liste précise]`
        },
        {
          category: 'contact' as const,
          title: "Contacts Stratégiques",
          description: "Services spécialisés pour débloquer les situations",
          actionableAdvice: "Contactez directement le service compétent avant l'échelon supérieur",
          relevantContacts: [
            "Médiation des services publics : mediation-info-service.fr",
            "Défenseur des droits : defenseurdesdroits.fr",
            "Assistance juridique gratuite : Points d'accès au droit"
          ]
        }
      ],
      'legal': [
        {
          category: 'technique' as const,
          title: "Technique de Négociation Juridique",
          description: "Approche structurée pour résoudre les conflits",
          actionableAdvice: "Toujours proposer une solution win-win avant l'escalade",
          insiderTip: "Documentez chaque échange par écrit (email de confirmation)"
        },
        {
          category: 'warning' as const,
          title: "Points de Vigilance Juridique",
          description: "Pièges à éviter dans les démarches légales",
          actionableAdvice: "Vérifiez toujours les délais de prescription et de recours",
          insiderTip: "Les délais se comptent en jours ouvrables sauf mention contraire"
        }
      ],
      'financial': [
        {
          category: 'tool' as const,
          title: "Calculateur d'Optimisation Fiscale",
          description: "Outils pour optimiser votre situation financière",
          actionableAdvice: "Utilisez les simulateurs officiels avant toute décision",
          relevantContacts: [
            "Simulateur impots.gouv.fr",
            "Calculatrice URSSAF en ligne",
            "Conseiller en investissement financier (CIF) agréé"
          ]
        }
      ]
    };

    return domainGems[domain as keyof typeof domainGems] || [];
  }

  /**
   * Pépites spécifiques au type d'étape
   */
  private getStepSpecificGems(stepTitle: string, problem: string): ValueGem[] {
    if (stepTitle.toLowerCase().includes('analyse')) {
      return [{
        category: 'technique',
        title: "Matrice d'Analyse SWOT Personnalisée",
        description: "Analyse structurée adaptée à votre situation",
        actionableAdvice: "Identifiez 3 forces, 3 faiblesses, 3 opportunités, 3 menaces",
        insiderTip: "Concentrez-vous sur les opportunités inexploitées - c'est là qu'est la valeur"
      }];
    }

    if (stepTitle.toLowerCase().includes('plan')) {
      return [{
        category: 'template',
        title: "Modèle de Plan d'Action Professionnel",
        description: "Structure éprouvée pour des plans d'action efficaces",
        actionableAdvice: "Chaque action doit avoir un responsable, une échéance et un critère de réussite",
        templateOrModel: `
PLAN D'ACTION:
1. Objectif: [SMART]
2. Actions: [Qui fait quoi quand]
3. Ressources: [Nécessaires et disponibles]
4. Risques: [Identifiés et mitigations]
5. Suivi: [Indicateurs et fréquence]`
      }];
    }

    return [];
  }

  /**
   * Pépites proactives basées sur le contenu
   */
  private getProactiveGems(problem: string, keywords: string[]): ValueGem[] {
    const gems: ValueGem[] = [];

    // Détection de stress/urgence
    if (problem.toLowerCase().includes('urgent') || problem.toLowerCase().includes('rapidement')) {
      gems.push({
        category: 'insight',
        title: "Gestion de l'Urgence Administrative",
        description: "Stratégie pour accélérer les procédures",
        actionableAdvice: "Privilégiez le contact direct (téléphone) avant l'écrit pour les urgences",
        insiderTip: "Le terme 'situation d'urgence médicale/sociale' accélère souvent le traitement"
      });
    }

    // Détection de problème récurrent
    if (problem.toLowerCase().includes('encore') || problem.toLowerCase().includes('déjà')) {
      gems.push({
        category: 'technique',
        title: "Stratégie de Résolution Définitive",
        description: "Comment éviter que le problème se reproduise",
        actionableAdvice: "Documentez la solution pour créer un processus réutilisable",
        insiderTip: "Demandez une confirmation écrite de la procédure à suivre à l'avenir"
      });
    }

    return gems;
  }

  /**
   * NOUVEAU : Vérification terminologique experte
   */
  private performExpertTerminologyCheck(problem: string, domain: string, response: string): ExpertTerminology {
    const terminologyChecks: TerminologyCheck[] = [];
    const precisionAlerts: string[] = [];
    const expertCorrections: TermCorrection[] = [];

    // Vérifications spécifiques par domaine
    const domainTerminologies = this.getDomainTerminologies();
    const currentDomainTerms = domainTerminologies[domain as keyof typeof domainTerminologies] || [];

    // Vérifier les termes dans le problème et la réponse
    const textToCheck = (problem + ' ' + response).toLowerCase();
    
    currentDomainTerms.forEach(termCheck => {
      const hasCorrectTerm = textToCheck.includes(termCheck.correctTerm.toLowerCase());
      const hasIncorrectTerm = termCheck.commonMistakes?.some(mistake => 
        textToCheck.includes(mistake.toLowerCase())
      );

      if (hasIncorrectTerm) {
        expertCorrections.push({
          incorrectUsage: termCheck.commonMistakes![0],
          correctUsage: termCheck.correctTerm,
          context: termCheck.context,
          importance: termCheck.importance
        });
      }

      terminologyChecks.push({
        term: termCheck.correctTerm,
        isCorrect: hasCorrectTerm && !hasIncorrectTerm,
        domain,
        correctTerm: termCheck.correctTerm,
        explanation: termCheck.explanation
      });
    });

    // Générer des alertes de précision
    if (expertCorrections.length > 0) {
      precisionAlerts.push(
        `⚠️ Attention terminologique : ${expertCorrections.length} terme(s) à préciser pour une expertise optimale`
      );
    }

    return {
      domainTerms: terminologyChecks,
      precisionAlerts,
      expertCorrections
    };
  }

  /**
   * Base de données de terminologies expertes par domaine
   */
  private getDomainTerminologies() {
    return {
      'administrative': [
        {
          correctTerm: "titre de séjour",
          commonMistakes: ["papiers", "autorisation de séjour"],
          context: "Droit des étrangers",
          importance: 'critical' as const,
          explanation: "Le terme juridique précis est 'titre de séjour', pas 'papiers'"
        },
        {
          correctTerm: "préfecture de police",
          commonMistakes: ["préfecture"],
          context: "Paris uniquement",
          importance: 'important' as const,
          explanation: "À Paris, c'est la préfecture de police qui gère les titres de séjour"
        }
      ],
      'legal': [
        {
          correctTerm: "mise en demeure",
          commonMistakes: ["demande formelle", "ultimatum"],
          context: "Procédure juridique",
          importance: 'critical' as const,
          explanation: "La 'mise en demeure' est l'acte juridique correct avant action en justice"
        }
      ],
      'financial': [
        {
          correctTerm: "cotisations sociales",
          commonMistakes: ["charges sociales"],
          context: "Droit social",
          importance: 'important' as const,
          explanation: "Les 'cotisations sociales' sont la terminologie officielle"
        }
      ]
    };
  }
  
  /**
   * Génère un prompt système expert-conseil selon les 5 principes + NOUVEAUTÉS STRATÉGIQUES
   */
  generateExpertSystemPrompt(stepIndex: number, problem: string, step: Step, contextFiles?: ContextFile[]): string {
    // Extraire les mots-clés du problème pour l'analyse contextuelle
    const keywords = this.extractKeywords(problem);
    const domain = knowledgeBaseService.identifyPrimaryDomain(keywords);

    // === NOUVELLES CAPACITÉS STRATÉGIQUES ===
    const strategicThread = this.identifyStrategicThread(problem, keywords, domain);
    const valueGems = this.generateValueGems(problem, keywords, domain, step.title);
    const expertTerminology = this.performExpertTerminologyCheck(problem, domain, '');

    // Générer le contexte des fichiers si disponibles
    const fileContext = contextFiles && contextFiles.length > 0 
      ? this.generateFileContextPrompt(contextFiles)
      : '';

    // Générer les éléments selon les 5 principes + améliorations stratégiques
    const basePersonality = this.getHumanPersonalityPrompt();
    const strategicInstructions = this.getStrategicInstructions(strategicThread);
    const valueGemsPrompt = this.getValueGemsPrompt(valueGems);
    const terminologyPrompt = this.getTerminologyPrompt(expertTerminology);
    const fundamentalKnowledge = this.getFundamentalKnowledgePrompt(step.task, domain, keywords, problem);
    const hypothesisInstructions = this.getHypothesisInstructions();
    const decompositionInstructions = this.getDecompositionInstructions();
    const actionPlanStructure = this.getActionPlanStructure();

    return `${basePersonality}

=== FIL ROUGE STRATÉGIQUE ===
${strategicInstructions}

=== PÉPITES DE VALEUR À INTÉGRER ===
${valueGemsPrompt}

=== VÉRIFICATION TERMINOLOGIQUE ===
${terminologyPrompt}

${fundamentalKnowledge}

VOTRE MISSION POUR CETTE ÉTAPE "${step.title}" :
${step.description}

${hypothesisInstructions}

${decompositionInstructions}

${actionPlanStructure}

CONTEXTE DU PROBLÈME À TRAITER :
${problem}

${fileContext}

TECHNIQUES À MOBILISER NATURELLEMENT : ${step.techniques.join(', ')}

RAPPEL CRITIQUE : Vous êtes un expert-conseil stratégique humain, pas un robot. Soyez chaleureux, proactif, orienté solutions ET enrichissez systématiquement vos conseils avec des pépites de valeur concrètes !`;
  }

  /**
   * Instructions pour maintenir le fil rouge stratégique
   */
  private getStrategicInstructions(strategicThread: StrategicThread): string {
    return `OBJECTIF PRIORITAIRE CENTRAL : ${strategicThread.coreObjective}
POINT DE LEVIER PRINCIPAL : ${strategicThread.leveragePoint}

RÈGLE STRATÉGIQUE ABSOLUE :
- Chaque conseil et recommandation DOIT explicitement servir l'objectif central
- Vérifiez constamment : "Cette action rapproche-t-elle du but principal ?"
- Mentionnez dans votre réponse comment chaque étape contribue à l'objectif prioritaire
- Gardez le cap sur le point de levier - c'est là qu'il faut concentrer l'effort

CONTINUITÉ STRATÉGIQUE :
${strategicThread.continuityCheck.map(check => `- ${check}`).join('\n')}

MESURE D'IMPACT :
${strategicThread.impactMeasurement.map(measure => `- ${measure}`).join('\n')}`;
  }

  /**
   * Instructions pour intégrer les pépites de valeur
   */
  private getValueGemsPrompt(valueGems: ValueGem[]): string {
    if (valueGems.length === 0) {
      return "AUCUNE PÉPITE SPÉCIFIQUE - Ajoutez des conseils proactifs selon votre expertise.";
    }

    let prompt = "PÉPITES À INTÉGRER DANS VOTRE RÉPONSE :\n\n";
    
    valueGems.forEach((gem, index) => {
      prompt += `${index + 1}. 💎 ${gem.title} (${gem.category.toUpperCase()})\n`;
      prompt += `   Description: ${gem.description}\n`;
      prompt += `   Conseil actionnable: ${gem.actionableAdvice}\n`;
      
      if (gem.insiderTip) {
        prompt += `   🎯 Astuce d'initié: ${gem.insiderTip}\n`;
      }
      
      if (gem.templateOrModel) {
        prompt += `   📋 Modèle: ${gem.templateOrModel}\n`;
      }
      
      if (gem.relevantContacts) {
        prompt += `   📞 Contacts: ${gem.relevantContacts.join(', ')}\n`;
      }
      
      prompt += '\n';
    });

    prompt += "OBLIGATION : Intégrez au moins 2-3 de ces pépites dans votre réponse de manière naturelle !";
    
    return prompt;
  }

  /**
   * Instructions pour la vérification terminologique
   */
  private getTerminologyPrompt(expertTerminology: ExpertTerminology): string {
    if (expertTerminology.expertCorrections.length === 0) {
      return "TERMINOLOGIE : Utilisez les termes techniques précis du domaine.";
    }

    let prompt = "ATTENTION TERMINOLOGIQUE EXPERTE :\n\n";
    
    expertTerminology.expertCorrections.forEach(correction => {
      prompt += `⚠️ ${correction.importance.toUpperCase()} : `;
      prompt += `Ne pas dire "${correction.incorrectUsage}" mais "${correction.correctUsage}"\n`;
      prompt += `   Contexte: ${correction.context}\n\n`;
    });

    prompt += "OBLIGATION : Utilisez UNIQUEMENT les termes techniques précis. Votre crédibilité d'expert en dépend !";
    
    return prompt;
  }

  /**
   * Génère un prompt contexte basé sur les fichiers téléchargés
   */
  private generateFileContextPrompt(contextFiles: ContextFile[]): string {
    if (!contextFiles || contextFiles.length === 0) {
      return '';
    }

    let fileContext = '\n\n=== CONTEXTE ADDITIONNEL FOURNI PAR L\'UTILISATEUR ===\n\n';
    fileContext += 'L\'utilisateur a fourni les documents suivants pour enrichir l\'analyse :\n\n';

    contextFiles.forEach((file, index) => {
      fileContext += `📄 DOCUMENT ${index + 1}: ${file.name}\n`;
      fileContext += `   Type: ${this.getFileTypeDescription(file.contentType)}\n`;
      fileContext += `   Taille: ${this.formatFileSize(file.size)}\n`;
      
      if (file.extractedText) {
        // Limiter le contexte pour éviter de surcharger le prompt
        const maxLength = 1500;
        const truncatedText = file.extractedText.length > maxLength 
          ? file.extractedText.substring(0, maxLength) + '...[contenu tronqué pour concision]'
          : file.extractedText;
        
        fileContext += `   Contenu pertinent:\n   ${truncatedText.replace(/\n/g, '\n   ')}\n\n`;
      }
    });

    fileContext += '=== FIN DU CONTEXTE ADDITIONNEL ===\n\n';
    fileContext += `INSTRUCTION SPÉCIALE: L'utilisateur a partagé ${contextFiles.length} document(s) avec vous. `;
    fileContext += 'Intégrez OBLIGATOIREMENT ces informations dans votre analyse. ';
    fileContext += 'Référencez-vous à ces documents dans vos recommandations et mentionnez explicitement comment ils enrichissent votre conseil. ';
    fileContext += 'Si les documents contiennent des détails importants, citez-les directement.\n\n';

    return fileContext;
  }

  /**
   * Obtient une description lisible du type de fichier
   */
  private getFileTypeDescription(contentType: string): string {
    switch (contentType) {
      case 'image':
        return 'Image/Visuel';
      case 'document':
        return 'Document';
      case 'text':
        return 'Fichier texte';
      default:
        return 'Fichier';
    }
  }

  /**
   * Formate la taille d'un fichier
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Prompt pour un comportement humain naturel et chaleureux
   */
  private getHumanPersonalityPrompt(): string {
    return `VOUS ÊTES ROONY - EXPERT-CONSEIL HUMAIN ET BIENVEILLANT

VOTRE PERSONNALITÉ :
- Vous êtes un consultant expérimenté, chaleureux et accessible
- Vous parlez comme un humain, pas comme un robot
- Vous montrez de l'empathie et de la compréhension
- Vous utilisez des expressions naturelles et des transitions fluides
- Vous rassurez tout en étant réaliste et professionnel

VOTRE APPROCHE CONVERSATIONNELLE :
- Commencez par une phrase empathique qui montre que vous comprenez la situation
- Utilisez des transitions naturelles : "Alors, voyons ensemble...", "Ce qui est important à retenir...", "Permettez-moi de vous éclairer..."
- Intégrez des éléments rassurants : "Bonne nouvelle", "Vous avez raison de...", "C'est tout à fait normal de..."
- Terminez par une note encourageante et des prochaines étapes claires

INTERDICTIONS ABSOLUES :
- Pas de jargon technique froid
- Pas de listes à puces mécaniques sans contexte
- Pas de "Veuillez noter que..." ou formulations robotiques
- Pas de questions multiples qui submergent l'utilisateur
- JAMAIS terminer sans solliciter explicitement la continuation

RÈGLE D'OR POUR CHAQUE RÉPONSE :
Terminez TOUJOURS par une phrase qui invite clairement l'utilisateur à agir :
- "Écrivez 'Continuer' pour passer à l'étape suivante"
- "Répondez-moi pour que nous puissions avancer ensemble"
- "Cliquez sur 'Envoyer' avec votre réponse pour poursuivre"
- "Dites-moi si cela vous convient pour continuer notre analyse"

RÈGLE D'OR POUR LA RECHERCHE D'INFORMATIONS :
TOUJOURS effectuer des recherches web quand nécessaire :
- Les modèles IA ne sont pas toujours à jour
- Le web contient les informations les plus récentes
- Les réglementations, lois et procédures changent régulièrement
- IMPÉRATIF : Rechercher sur le net pour valider et compléter vos connaissances
- Mentionner quand vous utilisez des sources web récentes : "Selon les dernières informations disponibles en ligne..."
- Privilégier les sources officielles (.gouv.fr, organismes publics, etc.)`;
  }

  /**
   * Instructions pour mobiliser la connaissance fondamentale
   */
  private getFundamentalKnowledgePrompt(task: Step['task'], domain: string, keywords: string[], problem: string): string {
    // Générer l'affirmation des certitudes basée sur la base de connaissances
    const certaintyStatement = knowledgeBaseService.generateCertaintyStatement(problem, keywords);
    const knowledgeBase = {
      'analyse': `PRINCIPES FONDAMENTAUX À AFFIRMER D'ABORD :
- Les droits de base qui s'appliquent dans ce contexte
- Les règles non négociables du domaine concerné
- Les protections légales existantes
- Les recours disponibles en cas de difficulté`,

      'génération': `PRINCIPES CRÉATIFS À MOBILISER :
- Les solutions éprouvées dans des cas similaires
- Les approches innovantes qui ont fait leurs preuves
- Les ressources disponibles et accessibles
- Les partenariats et synergies possibles`,

      'validation': `CRITÈRES DE VALIDATION À APPLIQUER :
- La conformité légale et réglementaire
- La faisabilité pratique et financière
- L'impact sur toutes les parties prenantes
- Les risques et les mesures de mitigation`,

      'synthèse': `ÉLÉMENTS DE SYNTHÈSE À STRUCTURER :
- Les acquis et certitudes établies
- Les recommandations prioritaires
- Le plan d'action chronologique
- Les points de vigilance et de suivi`
    };

    return `ÉTAPE 1 - AFFIRMEZ D'ABORD LES CERTITUDES :

${certaintyStatement}

${knowledgeBase[task]}

Commencez TOUJOURS par énoncer ce qui est sûr et acquis avant d'aborder les zones d'incertitude.

OBLIGATION DE RECHERCHE WEB :
- Si vous avez des doutes sur l'actualité des informations, recherchez sur le web
- Vérifiez toujours les dernières réglementations et procédures en ligne
- Mentionnez vos sources web : "Selon les dernières informations de [source officielle]..."
- Privilégiez les sites gouvernementaux (.gouv.fr) et organismes officiels`;
  }

  /**
   * Instructions pour formuler des hypothèses au lieu de poser des questions
   */
  private getHypothesisInstructions(): string {
    return `ÉTAPE 2 - FORMULEZ DES HYPOTHÈSES PLAUSIBLES :

Au lieu de poser des questions, formulez des hypothèses intelligentes :

FORMULATIONS À UTILISER :
- "En supposant que votre situation soit [cas le plus courant], alors..."
- "Si je comprends bien votre contexte, il y a deux scénarios probables..."
- "Dans la plupart des cas similaires que j'ai rencontrés..."
- "Compte tenu des éléments que vous mentionnez..."

RÈGLE D'OR : Ne posez une question QUE si la réponse change fondamentalement votre conseil.
Sinon, traitez les cas les plus probables avec vos hypothèses.

RECHERCHE WEB OBLIGATOIRE :
- Vérifiez en ligne les procédures récentes si nécessaire
- Consultez les sites officiels pour les dernières mises à jour
- Mentionnez : "J'ai vérifié les dernières informations disponibles en ligne et..."`;
  }

  /**
   * Instructions pour décomposer et hiérarchiser les problèmes
   */
  private getDecompositionInstructions(): string {
    return `ÉTAPE 3 - DÉCOMPOSEZ ET HIÉRARCHISEZ :

Pour tout problème complexe :

1. IDENTIFIEZ LES ENTITÉS DISTINCTES :
   - Séparez clairement chaque administration/organisme impliqué
   - Expliquez ce qui est indépendant et ce qui s'influence

2. HIÉRARCHISEZ LES OBJECTIFS :
   - Objectif principal (ce que veut vraiment l'utilisateur)
   - Objectifs secondaires (les obstacles à lever)
   - Points de vigilance (les risques à surveiller)

3. MONTREZ LES INTERACTIONS :
   - "Ces deux dossiers sont distincts, MAIS voici le point d'attention..."
   - "La procédure A n'affecte pas B, SAUF dans le cas où..."`;
  }

  /**
   * Structure obligatoire pour les réponses orientées action
   */
  private getActionPlanStructure(): string {
    return `ÉTAPE 4 - STRUCTUREZ VOTRE RÉPONSE COMME UNE CONSULTATION :

STRUCTURE OBLIGATOIRE :

1. 🤝 ACCUEIL EMPATHIQUE (1-2 phrases)
   - Montrez que vous comprenez la situation
   - Rassurez sur la faisabilité d'une solution

2. ✅ AFFIRMATION DES CERTITUDES (2-3 points clés)
   - "Ce qui est acquis dans votre situation..."
   - "Vos droits fondamentaux sont..."

3. 🔍 ANALYSE AVEC HYPOTHÈSES (décomposition structurée)
   - Traitez chaque entité/aspect séparément
   - Formulez des hypothèses sur les points flous

4. ⚠️ POINTS DE VIGILANCE (risques identifiés)
   - "Attention particulière à porter sur..."
   - "Le seul risque que je vois est..."

5. 🎯 PLAN D'ACTION CHRONOLOGIQUE (étapes concrètes)
   - "Voici ce que je vous recommande de faire..."
   - Numérotez les étapes avec des délais réalistes

6. 💪 CONCLUSION ENCOURAGEANTE
   - Résumez l'essentiel en une phrase
   - Donnez confiance pour la suite

7. 🚀 SOLLICITATION EXPLICITE (OBLIGATOIRE)
   - TOUJOURS terminer par "Écrivez 'Continuer' pour passer à l'étape suivante"
   - OU "Répondez-moi pour que nous puissions poursuivre ensemble"
   - OU "Cliquez sur 'Envoyer' avec votre réponse pour continuer"
   - JAMAIS laisser l'utilisateur sans savoir quoi faire ensuite`;
  }

  /**
   * Analyse si une réponse respecte les principes expert-conseil + CAPACITÉS STRATÉGIQUES
   */
  analyzeResponseQuality(response: string): {
    hasEmpathy: boolean;
    statesCertainties: boolean;
    usesHypotheses: boolean;
    isStructured: boolean;
    isHuman: boolean;
    hasStrategicThread: boolean;    // NOUVEAU
    includesValueGems: boolean;     // NOUVEAU
    usesExpertTerminology: boolean; // NOUVEAU
    score: number;
    suggestions: string[];
  } {
    const analysis = {
      hasEmpathy: this.checkEmpathy(response),
      statesCertainties: this.checkCertainties(response),
      usesHypotheses: this.checkHypotheses(response),
      isStructured: this.checkStructure(response),
      isHuman: this.checkHumanTouch(response),
      hasStrategicThread: this.checkStrategicThread(response),
      includesValueGems: this.checkValueGems(response),
      usesExpertTerminology: this.checkExpertTerminology(response),
      score: 0,
      suggestions: [] as string[]
    };

    // Calcul du score avec les nouvelles capacités
    const criteria = [
      analysis.hasEmpathy, 
      analysis.statesCertainties, 
      analysis.usesHypotheses, 
      analysis.isStructured, 
      analysis.isHuman,
      analysis.hasStrategicThread,
      analysis.includesValueGems,
      analysis.usesExpertTerminology
    ];
    analysis.score = (criteria.filter(Boolean).length / criteria.length) * 100;

    // Suggestions d'amélioration enrichies
    if (!analysis.hasEmpathy) analysis.suggestions.push("Ajouter une phrase empathique en début de réponse");
    if (!analysis.statesCertainties) analysis.suggestions.push("Commencer par affirmer les principes/droits acquis");
    if (!analysis.usesHypotheses) analysis.suggestions.push("Formuler des hypothèses au lieu de poser des questions");
    if (!analysis.isStructured) analysis.suggestions.push("Structurer en plan d'action chronologique");
    if (!analysis.isHuman) analysis.suggestions.push("Adopter un ton plus conversationnel et chaleureux");
    
    // Nouvelles suggestions stratégiques
    if (!analysis.hasStrategicThread) analysis.suggestions.push("🎯 Maintenir le fil rouge stratégique - lier chaque conseil à l'objectif central");
    if (!analysis.includesValueGems) analysis.suggestions.push("💎 Enrichir avec des pépites de valeur concrètes (outils, templates, contacts)");
    if (!analysis.usesExpertTerminology) analysis.suggestions.push("📚 Utiliser la terminologie technique précise du domaine");

    return analysis;
  }

  /**
   * NOUVEAU : Vérifie la présence du fil rouge stratégique
   */
  private checkStrategicThread(response: string): boolean {
    const strategicIndicators = [
      'objectif principal', 'objectif central', 'but prioritaire',
      'point de levier', 'élément clé', 'facteur critique',
      'contribue à', 'permet d\'atteindre', 'rapproche de',
      'cap sur', 'priorité absolue', 'essentiel pour'
    ];
    
    return strategicIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  /**
   * NOUVEAU : Vérifie la présence de pépites de valeur
   */
  private checkValueGems(response: string): boolean {
    const valueIndicators = [
      'modèle de', 'template', 'exemple concret',
      'astuce', 'conseil pratique', 'outil',
      'contact utile', 'ressource', 'technique éprouvée',
      'point d\'attention', 'vigilance', 'recommandation spécifique'
    ];
    
    return valueIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  /**
   * NOUVEAU : Vérifie l'usage de terminologie experte
   */
  private checkExpertTerminology(response: string): boolean {
    // Vérifie l'absence de termes approximatifs
    const approximateTerms = [
      'papiers', 'charges sociales', 'autorisation de séjour',
      'demande formelle', 'ultimatum'
    ];
    
    const hasApproximateTerms = approximateTerms.some(term => 
      response.toLowerCase().includes(term)
    );
    
    // Vérifie la présence de termes techniques précis
    const expertTerms = [
      'titre de séjour', 'cotisations sociales', 'mise en demeure',
      'préfecture de police', 'procédure administrative'
    ];
    
    const hasExpertTerms = expertTerms.some(term => 
      response.toLowerCase().includes(term)
    );
    
    return hasExpertTerms && !hasApproximateTerms;
  }

  private checkEmpathy(response: string): boolean {
    const empathyIndicators = [
      'je comprends', 'je vois que', 'c\'est normal', 'vous avez raison',
      'effectivement', 'tout à fait', 'bonne nouvelle', 'rassurez-vous'
    ];
    return empathyIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  private checkCertainties(response: string): boolean {
    const certaintyIndicators = [
      'ce qui est acquis', 'vos droits', 'principe fondamental',
      'règle établie', 'c\'est sûr', 'sans aucun doute'
    ];
    return certaintyIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  private checkHypotheses(response: string): boolean {
    const hypothesisIndicators = [
      'en supposant', 'si votre situation', 'dans la plupart des cas',
      'il y a deux scénarios', 'compte tenu de', 'probablement'
    ];
    return hypothesisIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  private checkStructure(response: string): boolean {
    // Vérifie la présence d'une structure organisée
    const structureIndicators = [
      'plan d\'action', 'étapes', 'recommandations',
      'voici ce que', 'première étape', 'ensuite'
    ];
    return structureIndicators.some(indicator => 
      response.toLowerCase().includes(indicator)
    );
  }

  private checkHumanTouch(response: string): boolean {
    const roboticPhrases = [
      'veuillez noter', 'il convient de', 'il est recommandé',
      'vous devez', 'il faut que', 'il est nécessaire'
    ];
    const humanPhrases = [
      'permettez-moi', 'voyons ensemble', 'ce qui est important',
      'je vous conseille', 'mon conseil', 'à mon avis'
    ];
    
    const hasRobotic = roboticPhrases.some(phrase => 
      response.toLowerCase().includes(phrase)
    );
    const hasHuman = humanPhrases.some(phrase => 
      response.toLowerCase().includes(phrase)
    );
    
    return hasHuman && !hasRobotic;
  }

  /**
   * Extrait les mots-clés pertinents d'un problème
   */
  private extractKeywords(problem: string): string[] {
    const text = problem.toLowerCase();
    const keywords: string[] = [];

    // Mots-clés administratifs
    const adminKeywords = ['préfecture', 'urssaf', 'titre', 'séjour', 'renouvellement', 'changement', 'statut', 'conjoint', 'famille', 'entreprise', 'activité', 'litige', 'cotisations', 'visa', 'autorisation', 'travail'];

    adminKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        keywords.push(keyword);
      }
    });

    // Extraire aussi les mots significatifs (plus de 4 caractères)
    const words = text.split(/\s+/).filter(word => word.length > 4);
    keywords.push(...words.slice(0, 10)); // Limiter à 10 mots max

    return [...new Set(keywords)]; // Supprimer les doublons
  }

  /**
   * Génère un résumé de qualification : certitudes / inconnues / objectifs
   */
  private generateQualificationSummary(
    userInput: string,
    decomposition: any,
    hypotheses: any,
    keywords: string[],
    domain: string,
    dependencyPriority: string[]
  ): string {
    const certainties = knowledgeBaseService.generateCertaintyStatement(userInput, keywords);

    // Variables clés & inconnues : extraire éléments non résolus des hypothèses et décomposition
    const unknowns: string[] = [];
    // Hypothèses - points à confirmer
    if (hypotheses?.primaryHypothesis) {
      hypotheses.primaryHypothesis.nextSteps?.forEach((ns: string) => {
        if (ns.toLowerCase().includes('confirmer') || ns.toLowerCase().includes('v rifier') || ns.toLowerCase().includes('confirmer')) {
          unknowns.push(ns);
        }
      });
    }

    // Ajouter quelques variables détectées via keywords
    const inferredUnknowns = keywords.filter(k => k.length > 5).slice(0, 5).map(k => `Vérifier : ${k}`);
    inferredUnknowns.forEach(u => unknowns.push(u));

    // Objectifs : depuis la décomposition
    const objectives = decomposition?.objectives ? [decomposition.objectives.primary, ...decomposition.objectives.secondary] : [];

    // Priorités par dépendance (chemin critique)
    const priorityPath = dependencyPriority && dependencyPriority.length > 0 ? `Ordre de priorité (chemin critique approximatif) : ${dependencyPriority.join(' > ')}` : '';

    let summary = `=== QUALIFICATION RAPIDE ===\n\n`;
    summary += `${certainties}\n\n`;
    summary += `❓ Variables clés & inconnues :\n`;
    if (unknowns.length === 0) {
      summary += `- Aucune inconnue explicite détectée.\n`;
    } else {
      unknowns.slice(0, 6).forEach(u => { summary += `- ${u}\n`; });
    }
    summary += `\n🎯 Objectifs identifiés :\n`;
    objectives.slice(0, 5).forEach((o: string) => { summary += `- ${o}\n`; });
    summary += `\n${priorityPath}\n\n`;

    return summary;
  }

  /**
   * Détecte des points de complexité spécifiques (multi-pays, juridique, normes)
   */
  private detectComplexityPoint(userInput: string, keywords: string[]): string | null {
    const triggers = ['international', 'juridique', 'multi-pays', 'r glementation', 'norme', 'normes', 'r gle', 'directive', 'convention', 'droit', 'pays', 'allemagne', 'espagne', 'italie', 'france'];
    const text = userInput.toLowerCase();

    const found = triggers.filter(t => text.includes(t) || keywords.some(k => k.includes(t)));
    if (found.length === 0) return null;

    let warning = `⚠️ POINT DE VIGILANCE SPÉCIFIQUE : détecté(s) ${found.join(', ')}.`;
    warning += `\nCe(s) point(s) nécessitent souvent une approche distincte ou une expertise locale.\n`; 
    warning += `Question ciblée : Avez-vous des conseils juridiques ou des contacts locaux pour ces juridictions ?`;
    return warning;
  }

  /**
   * Génère une analyse complète intégrant tous les services + CAPACITÉS STRATÉGIQUES
   */
  generateCompleteAnalysis(userInput: string, stepTitle: string, contextFiles?: ContextFile[]): string {
    const keywords = this.extractKeywords(userInput);
    const domain = knowledgeBaseService.identifyPrimaryDomain(keywords);

    // === NOUVEAUTÉS STRATÉGIQUES ===
    const strategicThread = this.identifyStrategicThread(userInput, keywords, domain);
    const valueGems = this.generateValueGems(userInput, keywords, domain, stepTitle);
    const expertTerminology = this.performExpertTerminologyCheck(userInput, domain, '');

    // Générer les hypothèses
    const hypothesisContext: HypothesisContext = {
      userInput,
      domain,
      keywords,
      stepType: 'analyse'
    };
    const hypotheses = hypothesisGeneratorService.generateHypotheses(hypothesisContext);

    // Décomposer le problème
    const decomposition = problemDecompositionService.decomposeProblem(userInput, domain, keywords);

    // Prioriser par dépendance (chemin critique simple)
    const dependencyPriority = problemDecompositionService.prioritizeByDependency(decomposition);

    // Ajouter le contexte des fichiers si disponible
    let enrichedUserInput = userInput;
    if (contextFiles && contextFiles.length > 0) {
      const fileContext = this.generateFileContextPrompt(contextFiles);
      enrichedUserInput = userInput + fileContext;
    }

    // Générer le résumé de qualification avec le fil rouge stratégique
    const qualification = this.generateStrategicQualificationSummary(
      userInput, decomposition, hypotheses, keywords, domain, dependencyPriority, strategicThread
    );

    // Détecter les points de complexité spécifiques
    const complexityPoint = this.detectComplexityPoint(userInput, keywords);

    // Enrichir avec les pépites de valeur
    const valueGemsContext = this.formatValueGemsForContext(valueGems);

    // Vérifications terminologiques
    const terminologyAlerts = this.formatTerminologyAlerts(expertTerminology);

    // Préfixer l'entrée enrichie par tous les éléments stratégiques
    enrichedUserInput = qualification + "\n" + 
                       valueGemsContext + "\n" + 
                       terminologyAlerts + "\n" + 
                       (complexityPoint ? complexityPoint + "\n\n" : "") + 
                       enrichedUserInput;

    // Formater en plan d'action avec les nouvelles capacités
    const actionPlan = actionPlanFormatterService.formatExpertConsultation(
      enrichedUserInput,
      domain,
      stepTitle,
      { 
        hypotheses, 
        decomposition, 
        strategicThread,
        valueGems,
        expertTerminology 
      }
    );

    return actionPlanFormatterService.formatActionPlanForResponse(actionPlan);
  }

  /**
   * Génère un résumé de qualification enrichi avec le fil rouge stratégique
   */
  private generateStrategicQualificationSummary(
    userInput: string,
    decomposition: any,
    hypotheses: any,
    keywords: string[],
    domain: string,
    dependencyPriority: string[],
    strategicThread: StrategicThread
  ): string {
    const certainties = knowledgeBaseService.generateCertaintyStatement(userInput, keywords);

    let summary = `=== QUALIFICATION STRATÉGIQUE ===\n\n`;
    
    // FIL ROUGE STRATÉGIQUE en premier
    summary += `🎯 OBJECTIF PRIORITAIRE : ${strategicThread.coreObjective}\n`;
    summary += `⚡ POINT DE LEVIER : ${strategicThread.leveragePoint}\n\n`;
    
    summary += `${certainties}\n\n`;

    // Variables clés & inconnues
    const unknowns: string[] = [];
    if (hypotheses?.primaryHypothesis) {
      hypotheses.primaryHypothesis.nextSteps?.forEach((ns: string) => {
        if (ns.toLowerCase().includes('confirmer') || ns.toLowerCase().includes('vérifier')) {
          unknowns.push(ns);
        }
      });
    }

    const inferredUnknowns = keywords.filter(k => k.length > 5).slice(0, 5).map(k => `Vérifier : ${k}`);
    inferredUnknowns.forEach(u => unknowns.push(u));

    summary += `❓ Variables clés & inconnues :\n`;
    if (unknowns.length === 0) {
      summary += `- Aucune inconnue explicite détectée.\n`;
    } else {
      unknowns.slice(0, 6).forEach(u => { summary += `- ${u}\n`; });
    }

    // Objectifs avec lien au fil rouge
    const objectives = decomposition?.objectives ? [decomposition.objectives.primary, ...decomposition.objectives.secondary] : [];
    summary += `\n🎯 Objectifs identifiés (liés à l'objectif prioritaire) :\n`;
    objectives.slice(0, 5).forEach((o: string) => { 
      summary += `- ${o} → Contribue à : ${strategicThread.coreObjective}\n`; 
    });

    // Chemin critique avec perspective stratégique
    const priorityPath = dependencyPriority && dependencyPriority.length > 0 
      ? `🔥 Ordre de priorité (chemin critique vers l'objectif) : ${dependencyPriority.join(' → ')}`
      : '';
    summary += `\n${priorityPath}\n\n`;

    return summary;
  }

  /**
   * Formate les pépites de valeur pour le contexte
   */
  private formatValueGemsForContext(valueGems: ValueGem[]): string {
    if (valueGems.length === 0) return '';

    let context = `=== PÉPITES DE VALEUR DISPONIBLES ===\n\n`;
    context += `💎 ${valueGems.length} pépites d'expertise identifiées pour enrichir votre conseil :\n\n`;
    
    valueGems.forEach((gem, index) => {
      context += `${index + 1}. [${gem.category.toUpperCase()}] ${gem.title}\n`;
      context += `   Action: ${gem.actionableAdvice}\n`;
      if (gem.insiderTip) {
        context += `   💡 Astuce: ${gem.insiderTip}\n`;
      }
      context += '\n';
    });

    context += `INSTRUCTION : Intégrez ces pépites naturellement dans votre réponse !\n\n`;
    
    return context;
  }

  /**
   * Formate les alertes terminologiques
   */
  private formatTerminologyAlerts(expertTerminology: ExpertTerminology): string {
    if (expertTerminology.expertCorrections.length === 0) return '';

    let alerts = `=== ALERTES TERMINOLOGIQUES EXPERTES ===\n\n`;
    
    expertTerminology.expertCorrections.forEach(correction => {
      alerts += `⚠️ [${correction.importance.toUpperCase()}] `;
      alerts += `Dire "${correction.correctUsage}" et non "${correction.incorrectUsage}"\n`;
      alerts += `   Context: ${correction.context}\n\n`;
    });

    alerts += `INSTRUCTION : Utilisez UNIQUEMENT les termes techniques précis !\n\n`;
    
    return alerts;
  }
}

// Instance singleton
export const expertConsultantService = new ExpertConsultantService();
