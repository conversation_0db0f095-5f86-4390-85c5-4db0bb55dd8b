import React from 'react';
import { generateStrategicReport, exportToPDF, type ReportData } from '../services/reportService';
import type { Message } from '../types';

interface ReportGeneratorProps {
  initialProblem: string;
  conversation: Message[];
  reasoningLog: string[];
  currentStepIndex: number;
  className?: string;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  initialProblem,
  conversation,
  reasoningLog,
  currentStepIndex,
  className = ''
}) => {
  const handleExportReport = (format: 'pdf' | 'md') => {
    const reportData: ReportData = {
      initialProblem,
      conversation,
      finalPrompt: 'Workflow en cours - Rapport intermédiaire',
      reasoningLog,
      currentStepIndex
    };

    const reportContent = generateStrategicReport(reportData);

    if (format === 'pdf') {
      exportToPDF(reportContent, `rapport-intermediaire-roony-${new Date().toISOString().split('T')[0]}`);
    } else {
      // Export en Markdown
      const blob = new Blob([reportContent], { type: 'text/markdown', endings: 'native' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rapport-intermediaire-roony-${new Date().toISOString().split('T')[0]}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Ne pas afficher si pas assez de données
  if (conversation.length < 2 || !initialProblem) {
    return null;
  }

  return (
    <div className={`bg-slate-800/30 rounded-lg border border-slate-600/50 overflow-hidden ${className}`}>
      <div className="p-2 bg-slate-700/30 border-b border-slate-600/50">
        <div className="flex items-center gap-2">
          <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-slate-300 font-medium">Rapport Stratégique</span>
        </div>
      </div>

      <div className="p-3 space-y-2">
        <p className="text-xs text-slate-400 mb-2">
          Générez un rapport détaillé de votre progression actuelle
        </p>
        
        <div className="flex gap-2">
          <button 
            onClick={() => handleExportReport('pdf')} 
            className="flex-1 bg-emerald-600/80 hover:bg-emerald-600 text-white text-xs font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-1"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            PDF
          </button>
          <button 
            onClick={() => handleExportReport('md')} 
            className="flex-1 bg-slate-600/80 hover:bg-slate-600 text-white text-xs font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-1"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            MD
          </button>
        </div>

        <div className="pt-2 border-t border-slate-700/50">
          <p className="text-xs text-slate-500 text-center">
            Étape {currentStepIndex}/15 • {conversation.length} échanges
          </p>
        </div>
      </div>
    </div>
  );
};
