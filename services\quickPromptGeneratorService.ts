import { sendMessageToAI } from './geminiService';
import type { Message } from '../types';
import type { ContextFile } from '../src/services/contextFileService';
import { dataDocsIntegrationService } from '../src/services/dataDocsIntegrationService';

export interface QuickPromptResult {
  optimizedPrompt: string;
  metaAnalysis: string;
  recommendations: string[];
  exportFormats: {
    txt: string;
    json: string;
    md: string;
  };
}

class QuickPromptGeneratorService {
  /**
   * Génère rapidement un prompt optimisé basé sur la description du problème
   * Utilise les techniques avancées des Data-Docs
   */
  async generateQuickPrompt(problemDescription: string, contextFiles?: ContextFile[]): Promise<QuickPromptResult> {
    // Initialiser le service Data-Docs si nécessaire
    await dataDocsIntegrationService.initialize();

    // Générer un prompt système enrichi avec les techniques avancées
    const systemPrompt = dataDocsIntegrationService.generateEnhancedSystemPrompt(problemDescription);

    // Ajouter le contexte des fichiers si disponible
    let enhancedProblemDescription = problemDescription;
    if (contextFiles && contextFiles.length > 0) {
      const contextInfo = contextFiles.map(file =>
        `Fichier contexte: ${file.name}\nContenu: ${file.extractedText?.substring(0, 500) || 'Contenu non disponible'}`
      ).join('\n\n');

      enhancedProblemDescription = `${problemDescription}\n\nCONTEXTE ADDITIONNEL:\n${contextInfo}`;
    }

    // Préparer les messages pour l'API
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: enhancedProblemDescription }
    ];

    // Utiliser le service Gemini pour générer le prompt
    const response = await sendMessageToAI(messages, 'génération');

    return this.parseQuickPromptResponse(response.content, problemDescription);
  }

  /**
   * Valide que la réponse ne contient pas de termes interdits
   */
  private validateResponse(response: string): string {
    let cleanedResponse = response;

    // Supprimer les termes interdits identifiés
    const forbiddenTerms = dataDocsIntegrationService.getForbiddenTermsList();
    forbiddenTerms.forEach(term => {
      const regex = new RegExp(term, 'gi');
      cleanedResponse = cleanedResponse.replace(regex, '[technique avancée]');
    });

    return cleanedResponse;
  }

  /**
   * Parse la réponse et structure les données
   * Nettoie les termes interdits et le formatage Markdown de la méta-analyse
   */
  private parseQuickPromptResponse(response: string, originalProblem: string): QuickPromptResult {
    // Valider et nettoyer la réponse
    const cleanedResponse = this.validateResponse(response);
    const sections = cleanedResponse.split('---');

    const optimizedPrompt = sections[0]
      ?.replace(/^### 🎯 Le Prompt Optimisé/i, '')
      ?.trim() || '';

    let metaAnalysis = sections[1]
      ?.replace(/^### 🔍 Méta-Analyse de la Construction/i, '')
      ?.trim() || '';

    // Nettoyer le formatage Markdown de la méta-analyse (selon vos consignes)
    metaAnalysis = this.cleanMarkdownFromMetaAnalysis(metaAnalysis);

    const recommendationsSection = sections[2] || '';
    const recommendations = this.extractRecommendations(recommendationsSection);

    // Générer les formats d'export avec les LLMs recommandés
    const exportFormats = this.generateExportFormats(optimizedPrompt, metaAnalysis, originalProblem);

    return {
      optimizedPrompt,
      metaAnalysis,
      recommendations,
      exportFormats
    };
  }

  /**
   * Nettoie le formatage Markdown de la méta-analyse (selon les consignes de Cisco)
   */
  private cleanMarkdownFromMetaAnalysis(metaAnalysis: string): string {
    return metaAnalysis
      // Supprimer les titres Markdown
      .replace(/^#{1,6}\s+/gm, '')
      // Supprimer les emphases (gras et italique)
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      // Supprimer les listes à puces Markdown
      .replace(/^[-*+]\s+/gm, '')
      // Supprimer les liens Markdown
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // Nettoyer les espaces multiples
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * Extrait les recommandations de la section correspondante
   */
  private extractRecommendations(section: string): string[] {
    const recommendations: string[] = [];
    const lines = section.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('-') || trimmedLine.startsWith('•') || trimmedLine.match(/^\d+\./)) {
        recommendations.push(trimmedLine.replace(/^[-•\d.]\s*/, ''));
      }
    }
    
    return recommendations.length > 0 ? recommendations : [
      'Testez le prompt avec différents exemples',
      'Ajustez les instructions selon vos besoins spécifiques',
      'Itérez sur les résultats pour améliorer la précision',
      'Documentez les performances pour optimisations futures',
      'Adaptez le ton et le style selon votre contexte'
    ];
  }

  /**
   * Génère les différents formats d'export
   */
  private generateExportFormats(prompt: string, metaAnalysis: string, originalProblem: string) {
    const timestamp = new Date().toLocaleString('fr-FR');
    
    // Format TXT
    const txt = `PROMPT OPTIMISÉ GÉNÉRÉ PAR ROONY
=====================================

Problème original: ${originalProblem}
Généré le: ${timestamp}

LE PROMPT OPTIMISÉ:
${prompt}

MÉTA-ANALYSE:
${metaAnalysis}

---
Généré par Roony - Studio Agentique FlexoDiv
`;

    // Format JSON
    const json = JSON.stringify({
      metadata: {
        generatedBy: 'Roony - Studio Agentique',
        timestamp: timestamp,
        originalProblem: originalProblem,
        version: '3.0'
      },
      optimizedPrompt: prompt,
      metaAnalysis: metaAnalysis,
      usage: {
        instructions: 'Copiez le prompt optimisé et utilisez-le dans votre LLM préféré',
        compatibleWith: dataDocsIntegrationService.getRecommendedLLMs()
      }
    }, null, 2);

    // Format Markdown
    const md = `# Prompt Optimisé - Génération Rapide

**Problème original:** ${originalProblem}  
**Généré le:** ${timestamp}  
**Par:** Roony - Studio Agentique FlexoDiv

## 🎯 Le Prompt Optimisé

${prompt}

---

## 🔍 Méta-Analyse de la Construction

${metaAnalysis}

---

## 🚀 Utilisation

1. **Copiez** le prompt optimisé ci-dessus
2. **Collez-le** dans votre LLM préféré (ChatGPT, Claude, Gemini, etc.)
3. **Adaptez** si nécessaire selon vos besoins spécifiques
4. **Testez** avec vos données réelles
5. **Itérez** pour améliorer les résultats

## 📞 Support

**Développé par:** FlexoDiv - Studio Agentique  
**Version:** Roony 3.0  
**Contact:** <EMAIL>

---

*Généré automatiquement par Roony Agent le ${timestamp}*
`;

    return { txt, json, md };
  }
}

export const quickPromptGeneratorService = new QuickPromptGeneratorService();
