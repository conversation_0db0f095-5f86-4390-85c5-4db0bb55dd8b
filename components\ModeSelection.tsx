import React from 'react';
import { Settings, Zap } from 'lucide-react';
import RoonyMascot from './RoonyMascot';

interface ModeSelectionProps {
  onSelectAnalysisMode: () => void;
  onSelectQuickPromptMode: () => void;
}

const ModeSelection: React.FC<ModeSelectionProps> = ({ 
  onSelectAnalysisMode, 
  onSelectQuickPromptMode 
}) => {
  return (
    <div className="flex-grow flex items-center justify-center p-6">
      <div className="w-full max-w-7xl">
        {/* Layout en 3 colonnes */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 h-full">
          
          {/* COLONNE 1 - Bienvenue */}
          <div className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 p-8 rounded-2xl border border-slate-600/50 flex flex-col justify-center items-center text-center">
            <div className="mb-6">
              <RoonyMascot 
                animation="idea" 
                size={100}
              />
            </div>
            
            <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-4">
              Bienvenue sur Roony Studio
            </h1>
            
            <p className="text-lg text-slate-300 mb-3">
              Votre assistant IA expert pour résoudre des problèmes complexes
            </p>
            
            <p className="text-slate-400 mb-6">
              Choisissez votre approche selon vos besoins
            </p>

            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3 justify-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Analyse experte par IA</span>
              </div>
              <div className="flex items-center gap-3 justify-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Solutions innovantes</span>
              </div>
              <div className="flex items-center gap-3 justify-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Résultats concrets</span>
              </div>
            </div>
          </div>

          {/* COLONNE 2 - Génération Rapide */}
          <div className="bg-gradient-to-br from-blue-900/50 to-indigo-900/50 p-8 rounded-2xl border border-blue-500/30 hover:border-blue-400/50 transition-all duration-300 transform hover:scale-105 cursor-pointer group relative"
               onClick={onSelectQuickPromptMode}>
            
            {/* Tooltip */}
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 translate-y-full opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-[99999]">
              <div className="bg-blue-600 text-white px-4 py-3 rounded-lg shadow-lg text-sm whitespace-nowrap">
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-blue-600"></div>
                <div className="font-semibold text-center mb-2">🚀 Choisissez "Génération Rapide" si :</div>
                <ul className="space-y-1 text-xs">
                  <li>• Vous avez une idée claire de ce que vous voulez</li>
                  <li>• Vous êtes pressé par le temps</li>
                  <li>• Vous voulez tester rapidement un concept</li>
                  <li>• Vous préférez l'action à l'analyse</li>
                </ul>
              </div>
            </div>
            
            <div className="text-center mb-6">
              <div className="bg-gradient-to-r from-blue-500 to-indigo-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Zap className="w-8 h-8 text-white" />
              </div>
              
              <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-400 mb-2">
                🚀 Génération Rapide
              </h2>
              
              <p className="text-slate-300 text-lg mb-4">
                Prompt optimisé en quelques secondes
              </p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-slate-300">Génération immédiate</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-slate-300">Export PDF, JSON, Markdown</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-slate-300">Prêt à utiliser ailleurs</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-slate-300">Idéal pour les besoins urgents</span>
              </div>
            </div>

            <div className="bg-blue-500/20 p-4 rounded-lg mb-6">
              <p className="text-sm text-blue-200">
                <strong>Parfait si :</strong> Vous avez besoin d'un prompt rapidement, vous savez déjà ce que vous voulez, ou vous voulez tester une idée.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold py-3 px-6 rounded-lg group-hover:from-blue-500 group-hover:to-indigo-500 transition-all">
                Génération Rapide
              </div>
              <p className="text-xs text-slate-400 mt-2">⏱️ ~30 secondes</p>
            </div>
          </div>

          {/* COLONNE 3 - Analyse Complète */}
          <div className="bg-gradient-to-br from-purple-900/50 to-pink-900/50 p-8 rounded-2xl border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300 transform hover:scale-105 cursor-pointer group relative"
               onClick={onSelectAnalysisMode}>
            
            {/* Tooltip */}
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 translate-y-full opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-[99999]">
              <div className="bg-purple-600 text-white px-4 py-3 rounded-lg shadow-lg text-sm whitespace-nowrap">
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-purple-600"></div>
                <div className="font-semibold text-center mb-2">⚙️ Choisissez "Analyse Complète" si :</div>
                <ul className="space-y-1 text-xs">
                  <li>• Votre problème est complexe ou peu défini</li>
                  <li>• Vous voulez explorer toutes les possibilités</li>
                  <li>• Vous avez besoin d'une stratégie détaillée</li>
                  <li>• Vous préférez l'analyse approfondie</li>
                </ul>
              </div>
            </div>
            
            <div className="text-center mb-6">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Settings className="w-8 h-8 text-white" />
              </div>
              
              <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-2">
                ⚙️ Analyse Complète
              </h2>
              
              <p className="text-slate-300 text-lg mb-4">
                Workflow expert en 14 étapes
              </p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Analyse approfondie du problème</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">14 étapes d'expertise</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Plan d'action détaillé</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-slate-300">Rapport stratégique complet</span>
              </div>
            </div>

            <div className="bg-purple-500/20 p-4 rounded-lg mb-6">
              <p className="text-sm text-purple-200">
                <strong>Parfait si :</strong> Vous avez un problème complexe, vous voulez une analyse poussée, ou vous cherchez une stratégie complète.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-3 px-6 rounded-lg group-hover:from-purple-500 group-hover:to-pink-500 transition-all">
                Analyse Complète
              </div>
              <p className="text-xs text-slate-400 mt-2">⏱️ ~15-20 minutes</p>
            </div>
          </div>
        </div>

        {/* Pied de page */}
        <div className="text-center mt-6">
          <p className="text-slate-500 text-sm">
            Développé par FlexoDiv - Studio Agentique • Version Roony 3.0
          </p>
        </div>
      </div>
    </div>
  );
};

export default ModeSelection;
