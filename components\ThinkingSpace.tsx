import React from 'react';

interface ThinkingSpaceProps {
  log: string[];
  className?: string;
}

const LogIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-sky-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
    </svg>
);


export const ThinkingSpace: React.FC<ThinkingSpaceProps> = ({ log, className = '' }) => {
  return (
    <div className={`${className} p-3 bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 flex flex-col`}>
      <h2 className="text-sm font-semibold mb-3 text-sky-300 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-sky-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
          Raisonnement
      </h2>
      <div className="flex-grow overflow-y-auto pr-1 -mr-1">
        <ul className="space-y-2">
          {log.length === 0 && <p className="text-slate-400 text-xs">Le journal des actions de l'agent apparaîtra ici...</p>}
          {log.map((entry, index) => (
            <li key={index} className="text-xs text-slate-300 bg-slate-700/50 p-2 rounded">
              <span className="font-mono text-sky-400 mr-1 text-xs">&gt;</span>{entry}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};